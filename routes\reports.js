const express = require('express');
const { getDatabase } = require('../database/init');
const { requireAuth } = require('./auth');
const path = require('path');
const fs = require('fs-extra');
const router = express.Router();

// Apply authentication middleware
router.use(requireAuth);

// Generate PDF report for a scan session
router.post('/generate/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;
        const userId = req.session.userId;
        const { format = 'pdf', includeDetails = true } = req.body;
        
        const db = getDatabase();
        
        // Get scan session data
        db.get('SELECT * FROM scan_sessions WHERE id = ? AND user_id = ?', 
            [sessionId, userId], 
            async (err, session) => {
                if (err) {
                    db.close();
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                if (!session) {
                    db.close();
                    return res.status(404).json({
                        success: false,
                        message: 'Scan session not found'
                    });
                }

                // Get vulnerabilities
                db.all('SELECT * FROM vulnerabilities WHERE session_id = ? ORDER BY severity DESC', 
                    [sessionId], 
                    async (err, vulnerabilities) => {
                        db.close();
                        
                        if (err) {
                            return res.status(500).json({
                                success: false,
                                message: 'Database error'
                            });
                        }

                        try {
                            const reportData = {
                                session,
                                vulnerabilities,
                                generatedAt: new Date().toISOString(),
                                generatedBy: req.session.username
                            };

                            let reportPath;
                            if (format === 'pdf') {
                                reportPath = await generatePDFReport(reportData, includeDetails);
                            } else {
                                reportPath = await generateHTMLReport(reportData, includeDetails);
                            }

                            // Save report record to database
                            const db2 = getDatabase();
                            db2.run('INSERT INTO reports (session_id, report_type, file_path) VALUES (?, ?, ?)',
                                [sessionId, format, reportPath], function(err) {
                                    db2.close();
                                    
                                    if (err) {
                                        console.error('Report save error:', err);
                                    }
                                }
                            );

                            res.json({
                                success: true,
                                message: 'Report generated successfully',
                                reportPath: `/api/reports/download/${path.basename(reportPath)}`,
                                reportId: reportPath
                            });

                        } catch (error) {
                            console.error('Report generation error:', error);
                            res.status(500).json({
                                success: false,
                                message: 'Failed to generate report'
                            });
                        }
                    }
                );
            }
        );

    } catch (error) {
        console.error('Generate report error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to generate report'
        });
    }
});

// Download report file
router.get('/download/:filename', (req, res) => {
    try {
        const { filename } = req.params;
        const filePath = path.join(__dirname, '../reports', filename);
        
        // Security check - ensure file exists and is in reports directory
        if (!fs.existsSync(filePath) || !filePath.startsWith(path.join(__dirname, '../reports'))) {
            return res.status(404).json({
                success: false,
                message: 'Report not found'
            });
        }

        res.download(filePath, (err) => {
            if (err) {
                console.error('Download error:', err);
                res.status(500).json({
                    success: false,
                    message: 'Failed to download report'
                });
            }
        });

    } catch (error) {
        console.error('Download report error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to download report'
        });
    }
});

// Get user's reports
router.get('/list', async (req, res) => {
    try {
        const userId = req.session.userId;
        
        const db = getDatabase();
        
        db.all(`SELECT r.*, s.target_url, s.created_at as scan_date 
                FROM reports r 
                JOIN scan_sessions s ON r.session_id = s.id 
                WHERE s.user_id = ? 
                ORDER BY r.created_at DESC`, 
            [userId], 
            (err, reports) => {
                db.close();
                
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                res.json({
                    success: true,
                    reports
                });
            }
        );

    } catch (error) {
        console.error('Get reports error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get reports'
        });
    }
});

// Delete report
router.delete('/:reportId', async (req, res) => {
    try {
        const { reportId } = req.params;
        const userId = req.session.userId;
        
        const db = getDatabase();
        
        // Get report info and verify ownership
        db.get(`SELECT r.*, s.user_id 
                FROM reports r 
                JOIN scan_sessions s ON r.session_id = s.id 
                WHERE r.id = ?`, 
            [reportId], 
            (err, report) => {
                if (err) {
                    db.close();
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                if (!report) {
                    db.close();
                    return res.status(404).json({
                        success: false,
                        message: 'Report not found'
                    });
                }

                if (report.user_id !== userId) {
                    db.close();
                    return res.status(403).json({
                        success: false,
                        message: 'Access denied'
                    });
                }

                // Delete file
                if (report.file_path && fs.existsSync(report.file_path)) {
                    fs.unlinkSync(report.file_path);
                }

                // Delete database record
                db.run('DELETE FROM reports WHERE id = ?', [reportId], function(err) {
                    db.close();
                    
                    if (err) {
                        return res.status(500).json({
                            success: false,
                            message: 'Database error'
                        });
                    }

                    res.json({
                        success: true,
                        message: 'Report deleted successfully'
                    });
                });
            }
        );

    } catch (error) {
        console.error('Delete report error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete report'
        });
    }
});

// Generate PDF Report (simplified version)
async function generatePDFReport(reportData, includeDetails) {
    const { session, vulnerabilities, generatedAt, generatedBy } = reportData;
    
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '../reports');
    await fs.ensureDir(reportsDir);
    
    const filename = `scan_report_${session.id}_${Date.now()}.html`;
    const filePath = path.join(reportsDir, filename);
    
    // Generate HTML report (can be converted to PDF using puppeteer later)
    const htmlContent = generateHTMLReportContent(reportData, includeDetails);
    
    await fs.writeFile(filePath, htmlContent, 'utf8');
    
    return filePath;
}

// Generate HTML Report
async function generateHTMLReport(reportData, includeDetails) {
    const { session } = reportData;
    
    const reportsDir = path.join(__dirname, '../reports');
    await fs.ensureDir(reportsDir);
    
    const filename = `scan_report_${session.id}_${Date.now()}.html`;
    const filePath = path.join(reportsDir, filename);
    
    const htmlContent = generateHTMLReportContent(reportData, includeDetails);
    
    await fs.writeFile(filePath, htmlContent, 'utf8');
    
    return filePath;
}

// Generate HTML Report Content
function generateHTMLReportContent(reportData, includeDetails) {
    const { session, vulnerabilities, generatedAt, generatedBy } = reportData;
    
    const severityColors = {
        'High': '#ff0040',
        'Medium': '#ffaa00',
        'Low': '#00ff41'
    };
    
    const severityCounts = vulnerabilities.reduce((acc, vuln) => {
        acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;
        return acc;
    }, {});

    return `
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير فحص الأمان - CyberWeb Hunter</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Arial', sans-serif; background: #f5f5f5; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { background: linear-gradient(135deg, #0a0a0a, #1a1a1a); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .logo { font-size: 2rem; font-weight: bold; color: #00ff41; margin-bottom: 10px; }
        .report-title { font-size: 1.5rem; margin-bottom: 20px; }
        .report-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
        .info-item { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; }
        .section { background: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .section-title { font-size: 1.3rem; color: #333; margin-bottom: 20px; border-bottom: 2px solid #00ff41; padding-bottom: 10px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .summary-card { background: linear-gradient(135deg, #f8f9fa, #e9ecef); padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #00ff41; }
        .summary-number { font-size: 2rem; font-weight: bold; color: #333; }
        .summary-label { color: #666; margin-top: 5px; }
        .vulnerability { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin-bottom: 15px; }
        .vuln-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        .vuln-type { font-weight: bold; font-size: 1.1rem; }
        .severity { padding: 5px 15px; border-radius: 20px; color: white; font-weight: bold; font-size: 0.9rem; }
        .severity.High { background: #ff0040; }
        .severity.Medium { background: #ffaa00; }
        .severity.Low { background: #00ff41; color: #000; }
        .vuln-details { margin-top: 15px; }
        .detail-row { margin-bottom: 10px; }
        .detail-label { font-weight: bold; color: #555; }
        .footer { text-align: center; color: #666; margin-top: 40px; padding: 20px; border-top: 1px solid #dee2e6; }
        @media print { body { background: white; } .container { max-width: none; } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🛡️ CyberWeb Hunter</div>
            <div class="report-title">تقرير فحص الأمان الشامل</div>
            <div class="report-info">
                <div class="info-item">
                    <strong>الموقع المستهدف:</strong><br>
                    ${session.target_url}
                </div>
                <div class="info-item">
                    <strong>تاريخ الفحص:</strong><br>
                    ${new Date(session.created_at).toLocaleString('ar-SA')}
                </div>
                <div class="info-item">
                    <strong>نوع الفحص:</strong><br>
                    ${JSON.parse(session.scan_type || '[]').join(', ')}
                </div>
                <div class="info-item">
                    <strong>تم إنشاؤه بواسطة:</strong><br>
                    ${generatedBy}
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📊 ملخص النتائج</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="summary-number">${vulnerabilities.length}</div>
                    <div class="summary-label">إجمالي الثغرات</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" style="color: #ff0040;">${severityCounts.High || 0}</div>
                    <div class="summary-label">ثغرات عالية الخطورة</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" style="color: #ffaa00;">${severityCounts.Medium || 0}</div>
                    <div class="summary-label">ثغرات متوسطة الخطورة</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" style="color: #00ff41;">${severityCounts.Low || 0}</div>
                    <div class="summary-label">ثغرات منخفضة الخطورة</div>
                </div>
            </div>
        </div>

        ${vulnerabilities.length > 0 ? `
        <div class="section">
            <h2 class="section-title">🔍 تفاصيل الثغرات المكتشفة</h2>
            ${vulnerabilities.map((vuln, index) => `
                <div class="vulnerability">
                    <div class="vuln-header">
                        <div class="vuln-type">${index + 1}. ${vuln.vulnerability_type}</div>
                        <div class="severity ${vuln.severity}">${vuln.severity}</div>
                    </div>
                    <div class="vuln-details">
                        <div class="detail-row">
                            <span class="detail-label">الوصف:</span> ${vuln.description}
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الرابط:</span> ${vuln.url}
                        </div>
                        ${includeDetails && vuln.payload ? `
                        <div class="detail-row">
                            <span class="detail-label">الحمولة:</span> <code>${vuln.payload}</code>
                        </div>
                        ` : ''}
                        ${includeDetails && vuln.evidence ? `
                        <div class="detail-row">
                            <span class="detail-label">الدليل:</span> ${vuln.evidence}
                        </div>
                        ` : ''}
                        <div class="detail-row">
                            <span class="detail-label">التوصية:</span> ${vuln.recommendation}
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
        ` : `
        <div class="section">
            <h2 class="section-title">✅ نتائج الفحص</h2>
            <p style="text-align: center; color: #00ff41; font-size: 1.2rem; padding: 40px;">
                🎉 تهانينا! لم يتم العثور على أي ثغرات أمنية في الموقع المستهدف.
            </p>
        </div>
        `}

        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة CyberWeb Hunter في ${new Date(generatedAt).toLocaleString('ar-SA')}</p>
            <p style="margin-top: 10px; font-size: 0.9rem;">
                ⚠️ هذا التقرير للاستخدام التعليمي والأخلاقي فقط. يرجى استخدامه بمسؤولية.
            </p>
        </div>
    </div>
</body>
</html>
    `;
}

module.exports = router;
