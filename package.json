{"name": "cyberweb-hunter", "version": "1.0.0", "description": "CyberWeb Hunter - Ethical Penetration Testing Tool", "main": "server.js", "scripts": {"start": "node server.js", "run": "node run.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build": "webpack --mode production", "build:dev": "webpack --mode development", "lint": "eslint .", "lint:fix": "eslint . --fix", "setup": "node scripts/setup.js", "backup": "node scripts/backup.js", "restore": "node scripts/restore.js", "clean": "rimraf node_modules package-lock.json && npm install", "security-check": "npm audit", "update-deps": "npm update", "docker:build": "docker build -t cyberweb-hunter .", "docker:run": "docker run -p 3000:3000 cyberweb-hunter"}, "keywords": ["penetration-testing", "security", "ethical-hacking", "vulnerability-scanner", "cybersecurity"], "author": "CyberWeb Hunter Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "express-session": "^1.17.3", "bcryptjs": "^2.4.3", "sqlite3": "^5.1.6", "body-parser": "^1.20.2", "cors": "^2.8.5", "helmet": "^7.0.0", "axios": "^1.4.0", "cheerio": "^1.0.0-rc.12", "puppeteer": "^20.7.2", "node-html-parser": "^6.1.5", "ssl-checker": "^2.0.7", "socks-proxy-agent": "^8.0.1", "tor-request": "^3.1.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "multer": "^1.4.5-lts.1", "express-rate-limit": "^6.8.1", "validator": "^13.9.0", "crypto": "^1.0.1", "fs-extra": "^11.1.1", "path": "^0.12.7", "url": "^0.11.1", "dns": "^0.2.2", "archiver": "^5.3.1", "unzipper": "^0.10.11"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.1", "webpack": "^5.88.1", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=16.0.0"}}