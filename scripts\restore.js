#!/usr/bin/env node

// Restore script for CyberWeb Hunter

const fs = require('fs-extra');
const path = require('path');
const unzipper = require('unzipper');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    red: '\x1b[31m',
    cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
    console.log(colors[color] + message + colors.reset);
}

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function listBackups() {
    try {
        if (!await fs.pathExists('backups')) {
            colorLog('📁 No backups directory found', 'yellow');
            return [];
        }

        const files = await fs.readdir('backups');
        const backups = files.filter(file => file.endsWith('.zip'));
        
        if (backups.length === 0) {
            colorLog('📦 No backup files found', 'yellow');
            return [];
        }

        colorLog('\n📋 Available backups:', 'blue');
        backups.forEach((backup, index) => {
            colorLog(`${index + 1}. ${backup}`, 'cyan');
        });

        return backups;

    } catch (error) {
        colorLog(`❌ Error listing backups: ${error.message}`, 'red');
        return [];
    }
}

async function selectBackup(backups) {
    if (backups.length === 0) {
        return null;
    }

    const choice = await question('\n🔢 Select backup number (or 0 to cancel): ');
    const index = parseInt(choice) - 1;

    if (choice === '0') {
        colorLog('❌ Restore cancelled', 'yellow');
        return null;
    }

    if (isNaN(index) || index < 0 || index >= backups.length) {
        colorLog('❌ Invalid selection', 'red');
        return null;
    }

    return backups[index];
}

async function confirmRestore() {
    colorLog('\n⚠️  WARNING: This will overwrite existing data!', 'yellow');
    colorLog('📁 The following will be replaced:', 'yellow');
    colorLog('   - Database files', 'yellow');
    colorLog('   - Configuration files', 'yellow');
    colorLog('   - Reports (if any)', 'yellow');
    colorLog('   - Logs (if any)', 'yellow');

    const confirm = await question('\n❓ Are you sure you want to continue? (yes/no): ');
    return confirm.toLowerCase() === 'yes';
}

async function createBackupOfCurrent() {
    colorLog('💾 Creating backup of current data...', 'blue');
    
    try {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `pre-restore-backup-${timestamp}`;
        const backupDir = path.join('backups', backupName);

        await fs.ensureDir(backupDir);

        // Backup current database
        if (await fs.pathExists('database')) {
            await fs.copy('database', path.join(backupDir, 'database'));
            colorLog('✅ Current database backed up', 'green');
        }

        // Backup current config
        const configFiles = ['.env', 'package.json'];
        for (const file of configFiles) {
            if (await fs.pathExists(file)) {
                await fs.copy(file, path.join(backupDir, file));
            }
        }

        colorLog(`✅ Pre-restore backup created: ${backupName}`, 'green');
        return backupDir;

    } catch (error) {
        colorLog(`⚠️  Failed to create pre-restore backup: ${error.message}`, 'yellow');
        return null;
    }
}

async function restoreFromBackup(backupFile) {
    const backupPath = path.join('backups', backupFile);
    
    colorLog(`🔄 Restoring from: ${backupFile}`, 'blue');

    try {
        // Create temporary extraction directory
        const tempDir = path.join('temp', 'restore');
        await fs.ensureDir(tempDir);

        // Extract backup
        colorLog('📦 Extracting backup...', 'blue');
        await fs.createReadStream(backupPath)
            .pipe(unzipper.Extract({ path: tempDir }))
            .promise();

        // Restore database
        if (await fs.pathExists(path.join(tempDir, 'database'))) {
            await fs.remove('database');
            await fs.move(path.join(tempDir, 'database'), 'database');
            colorLog('✅ Database restored', 'green');
        }

        // Restore configuration files
        const configFiles = ['.env', 'package.json'];
        for (const file of configFiles) {
            const sourcePath = path.join(tempDir, file);
            if (await fs.pathExists(sourcePath)) {
                await fs.copy(sourcePath, file);
                colorLog(`✅ ${file} restored`, 'green');
            }
        }

        // Restore reports
        if (await fs.pathExists(path.join(tempDir, 'reports'))) {
            await fs.ensureDir('reports');
            await fs.copy(path.join(tempDir, 'reports'), 'reports');
            colorLog('✅ Reports restored', 'green');
        }

        // Restore logs
        if (await fs.pathExists(path.join(tempDir, 'logs'))) {
            await fs.ensureDir('logs');
            await fs.copy(path.join(tempDir, 'logs'), 'logs');
            colorLog('✅ Logs restored', 'green');
        }

        // Clean up temporary directory
        await fs.remove('temp');

        colorLog('✅ Restore completed successfully', 'green');

    } catch (error) {
        colorLog(`❌ Restore failed: ${error.message}`, 'red');
        throw error;
    }
}

async function main() {
    colorLog('🛡️  CyberWeb Hunter Restore', 'blue');
    colorLog('=============================', 'blue');

    try {
        // List available backups
        const backups = await listBackups();
        if (backups.length === 0) {
            colorLog('❌ No backups available for restore', 'red');
            process.exit(1);
        }

        // Select backup
        const selectedBackup = await selectBackup(backups);
        if (!selectedBackup) {
            process.exit(0);
        }

        // Confirm restore
        if (!await confirmRestore()) {
            colorLog('❌ Restore cancelled', 'yellow');
            process.exit(0);
        }

        // Create backup of current state
        await createBackupOfCurrent();

        // Perform restore
        await restoreFromBackup(selectedBackup);

        colorLog('\n📋 Restore Summary:', 'blue');
        colorLog(`📦 Restored from: ${selectedBackup}`, 'green');
        colorLog('✅ Restore completed successfully', 'green');
        colorLog('\n🔄 Please restart the server to apply changes', 'cyan');

    } catch (error) {
        colorLog(`❌ Restore process failed: ${error.message}`, 'red');
        process.exit(1);
    } finally {
        rl.close();
    }
}

// Run restore if called directly
if (require.main === module) {
    main();
}

module.exports = { restoreFromBackup, listBackups };
