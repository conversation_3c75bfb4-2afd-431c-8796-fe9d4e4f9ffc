#!/usr/bin/env node

// Backup script for CyberWeb Hunter

const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    red: '\x1b[31m'
};

function colorLog(message, color = 'reset') {
    console.log(colors[color] + message + colors.reset);
}

async function createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `cyberweb-backup-${timestamp}.zip`;
    const backupPath = path.join('backups', backupName);

    colorLog('🔄 Creating backup...', 'blue');
    
    try {
        // Ensure backup directory exists
        await fs.ensureDir('backups');

        // Create write stream
        const output = fs.createWriteStream(backupPath);
        const archive = archiver('zip', {
            zlib: { level: 9 } // Maximum compression
        });

        // Listen for archive events
        output.on('close', () => {
            const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
            colorLog(`✅ Backup created successfully: ${backupName}`, 'green');
            colorLog(`📦 Size: ${sizeInMB} MB`, 'blue');
        });

        archive.on('error', (err) => {
            throw err;
        });

        // Pipe archive data to the file
        archive.pipe(output);

        // Add database files
        if (await fs.pathExists('database')) {
            archive.directory('database/', 'database/');
            colorLog('📁 Added database files', 'blue');
        }

        // Add configuration files
        const configFiles = ['.env', 'package.json', 'package-lock.json'];
        for (const file of configFiles) {
            if (await fs.pathExists(file)) {
                archive.file(file, { name: file });
                colorLog(`📄 Added ${file}`, 'blue');
            }
        }

        // Add reports (last 30 days only)
        if (await fs.pathExists('reports')) {
            const reports = await fs.readdir('reports');
            const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
            
            for (const report of reports) {
                const reportPath = path.join('reports', report);
                const stats = await fs.stat(reportPath);
                
                if (stats.mtime.getTime() > thirtyDaysAgo) {
                    archive.file(reportPath, { name: `reports/${report}` });
                }
            }
            colorLog('📊 Added recent reports', 'blue');
        }

        // Add logs (last 7 days only)
        if (await fs.pathExists('logs')) {
            const logs = await fs.readdir('logs');
            const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
            
            for (const log of logs) {
                const logPath = path.join('logs', log);
                const stats = await fs.stat(logPath);
                
                if (stats.mtime.getTime() > sevenDaysAgo) {
                    archive.file(logPath, { name: `logs/${log}` });
                }
            }
            colorLog('📝 Added recent logs', 'blue');
        }

        // Finalize the archive
        await archive.finalize();

        return backupPath;

    } catch (error) {
        colorLog(`❌ Backup failed: ${error.message}`, 'red');
        throw error;
    }
}

async function cleanOldBackups() {
    colorLog('🧹 Cleaning old backups...', 'blue');
    
    try {
        if (!await fs.pathExists('backups')) {
            return;
        }

        const backups = await fs.readdir('backups');
        const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
        let deletedCount = 0;

        for (const backup of backups) {
            const backupPath = path.join('backups', backup);
            const stats = await fs.stat(backupPath);
            
            if (stats.mtime.getTime() < thirtyDaysAgo) {
                await fs.remove(backupPath);
                deletedCount++;
                colorLog(`🗑️  Deleted old backup: ${backup}`, 'yellow');
            }
        }

        if (deletedCount === 0) {
            colorLog('✅ No old backups to clean', 'green');
        } else {
            colorLog(`✅ Cleaned ${deletedCount} old backup(s)`, 'green');
        }

    } catch (error) {
        colorLog(`⚠️  Failed to clean old backups: ${error.message}`, 'yellow');
    }
}

async function main() {
    colorLog('🛡️  CyberWeb Hunter Backup', 'blue');
    colorLog('============================', 'blue');
    
    try {
        const backupPath = await createBackup();
        await cleanOldBackups();
        
        colorLog('\n📋 Backup Summary:', 'blue');
        colorLog(`📦 Backup file: ${backupPath}`, 'green');
        colorLog('✅ Backup completed successfully', 'green');
        
    } catch (error) {
        colorLog(`❌ Backup process failed: ${error.message}`, 'red');
        process.exit(1);
    }
}

// Run backup if called directly
if (require.main === module) {
    main();
}

module.exports = { createBackup, cleanOldBackups };
