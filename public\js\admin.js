// Admin Panel functionality for CyberWeb Hunter

class AdminPanel {
    constructor() {
        this.currentUser = null;
        this.currentSection = 'overview';
        
        this.init();
    }

    async init() {
        await this.checkAdminAuth();
        this.setupEventListeners();
        this.setupNavigation();
        await this.loadOverviewData();
    }

    async checkAdminAuth() {
        try {
            const response = await fetch('/api/auth/status');
            const data = await response.json();

            if (!data.success || !data.authenticated || data.user.role !== 'admin') {
                window.location.href = '/';
                return;
            }

            this.currentUser = data.user;
            document.getElementById('adminUsername').textContent = data.user.username;

        } catch (error) {
            console.error('Admin auth check error:', error);
            window.location.href = '/';
        }
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
            });
        });

        // Search functionality
        document.getElementById('userSearch')?.addEventListener('input', (e) => {
            this.filterTable('usersTableBody', e.target.value);
        });

        document.getElementById('scanSearch')?.addEventListener('input', (e) => {
            this.filterTable('scansTableBody', e.target.value);
        });

        document.getElementById('vulnSearch')?.addEventListener('input', (e) => {
            this.filterTable('vulnerabilitiesTableBody', e.target.value);
        });

        // Filter functionality
        document.getElementById('scanStatusFilter')?.addEventListener('change', (e) => {
            this.filterTableByColumn('scansTableBody', 4, e.target.value);
        });

        document.getElementById('vulnSeverityFilter')?.addEventListener('change', (e) => {
            this.filterTableByColumn('vulnerabilitiesTableBody', 2, e.target.value);
        });

        document.getElementById('vulnTypeFilter')?.addEventListener('change', (e) => {
            this.filterTableByColumn('vulnerabilitiesTableBody', 1, e.target.value);
        });
    }

    setupNavigation() {
        const hash = window.location.hash.substring(1) || 'overview';
        this.showSection(hash);

        window.addEventListener('hashchange', () => {
            const section = window.location.hash.substring(1) || 'overview';
            this.showSection(section);
        });
    }

    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`)?.classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`)?.classList.add('active');

        // Update URL
        window.location.hash = sectionName;
        this.currentSection = sectionName;

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'overview':
                await this.loadOverviewData();
                break;
            case 'users':
                await this.loadUsers();
                break;
            case 'scans':
                await this.loadScans();
                break;
            case 'vulnerabilities':
                await this.loadVulnerabilities();
                break;
            case 'reports':
                await this.loadReports();
                break;
            case 'settings':
                await this.loadSettings();
                break;
        }
    }

    async loadOverviewData() {
        try {
            const response = await fetch('/api/admin/stats');
            const data = await response.json();

            if (data.success) {
                document.getElementById('totalUsers').textContent = data.stats.totalUsers || 0;
                document.getElementById('totalScans').textContent = data.stats.totalScans || 0;
                document.getElementById('totalVulnerabilities').textContent = data.stats.totalVulnerabilities || 0;
                document.getElementById('completedScans').textContent = data.stats.completedScans || 0;
            }

            // Load recent activity
            await this.loadRecentActivity();

        } catch (error) {
            console.error('Load overview data error:', error);
        }
    }

    async loadRecentActivity() {
        try {
            const response = await fetch('/api/admin/activity');
            const data = await response.json();

            if (data.success) {
                this.displayRecentActivity(data.activities);
            }

        } catch (error) {
            console.error('Load recent activity error:', error);
        }
    }

    displayRecentActivity(activities) {
        const container = document.getElementById('recentActivity');
        
        if (activities.length === 0) {
            container.innerHTML = '<div class="no-data">لا توجد أنشطة حديثة</div>';
            return;
        }

        container.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="fas fa-${this.getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <h5>${this.getActivityTitle(activity)}</h5>
                    <p>${activity.username} - ${new Date(activity.created_at).toLocaleDateString('ar-SA')}</p>
                </div>
            </div>
        `).join('');
    }

    getActivityIcon(type) {
        const icons = {
            'scan': 'search',
            'user': 'user',
            'report': 'file-pdf'
        };
        return icons[type] || 'info-circle';
    }

    getActivityTitle(activity) {
        switch (activity.type) {
            case 'scan':
                return `فحص جديد: ${activity.description}`;
            default:
                return activity.description;
        }
    }

    async loadUsers() {
        try {
            const response = await fetch('/api/admin/users');
            const data = await response.json();

            if (data.success) {
                this.displayUsers(data.users);
            }

        } catch (error) {
            console.error('Load users error:', error);
        }
    }

    displayUsers(users) {
        const tbody = document.getElementById('usersTableBody');
        
        if (users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="loading">لا توجد مستخدمين</td></tr>';
            return;
        }

        tbody.innerHTML = users.map(user => `
            <tr>
                <td>${user.id}</td>
                <td>${user.username}</td>
                <td>${user.email || 'غير محدد'}</td>
                <td><span class="status-badge status-${user.role}">${user.role === 'admin' ? 'مسؤول' : 'مستخدم'}</span></td>
                <td>${new Date(user.created_at).toLocaleDateString('ar-SA')}</td>
                <td>${user.last_login ? new Date(user.last_login).toLocaleDateString('ar-SA') : 'لم يسجل دخول'}</td>
                <td><span class="status-badge status-${user.is_active ? 'active' : 'inactive'}">${user.is_active ? 'نشط' : 'غير نشط'}</span></td>
                <td>
                    <button class="action-btn action-btn-edit" onclick="adminPanel.editUser(${user.id})">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn action-btn-delete" onclick="adminPanel.deleteUser(${user.id})" ${user.id === 1 ? 'disabled' : ''}>
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    async loadScans() {
        try {
            const response = await fetch('/api/admin/scans');
            const data = await response.json();

            if (data.success) {
                this.displayScans(data.scans);
            }

        } catch (error) {
            console.error('Load scans error:', error);
        }
    }

    displayScans(scans) {
        const tbody = document.getElementById('scansTableBody');
        
        if (scans.length === 0) {
            tbody.innerHTML = '<tr><td colspan="9" class="loading">لا توجد عمليات فحص</td></tr>';
            return;
        }

        tbody.innerHTML = scans.map(scan => `
            <tr>
                <td>${scan.id}</td>
                <td>${scan.username || 'غير معروف'}</td>
                <td>${scan.target_url}</td>
                <td>${this.formatScanTypes(scan.scan_type)}</td>
                <td><span class="status-badge status-${scan.status}">${this.getStatusText(scan.status)}</span></td>
                <td>${new Date(scan.created_at).toLocaleDateString('ar-SA')}</td>
                <td>${scan.completed_at ? new Date(scan.completed_at).toLocaleDateString('ar-SA') : 'غير مكتمل'}</td>
                <td>${this.getVulnerabilityCount(scan.results)}</td>
                <td>
                    <button class="action-btn action-btn-view" onclick="adminPanel.viewScan(${scan.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn action-btn-delete" onclick="adminPanel.deleteScan(${scan.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    formatScanTypes(scanTypeJson) {
        try {
            const types = JSON.parse(scanTypeJson || '[]');
            return types.join(', ') || 'غير محدد';
        } catch (error) {
            return 'غير محدد';
        }
    }

    getStatusText(status) {
        const statusMap = {
            'completed': 'مكتمل',
            'running': 'قيد التشغيل',
            'failed': 'فاشل',
            'stopped': 'متوقف'
        };
        return statusMap[status] || status;
    }

    getVulnerabilityCount(resultsJson) {
        try {
            const results = JSON.parse(resultsJson || '{}');
            return results.vulnerabilities_found || 0;
        } catch (error) {
            return 0;
        }
    }

    async loadVulnerabilities() {
        try {
            const response = await fetch('/api/admin/vulnerabilities');
            const data = await response.json();

            if (data.success) {
                this.displayVulnerabilities(data.vulnerabilities);
            }

        } catch (error) {
            console.error('Load vulnerabilities error:', error);
        }
    }

    displayVulnerabilities(vulnerabilities) {
        const tbody = document.getElementById('vulnerabilitiesTableBody');
        
        if (vulnerabilities.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" class="loading">لا توجد ثغرات</td></tr>';
            return;
        }

        tbody.innerHTML = vulnerabilities.map(vuln => `
            <tr>
                <td>${vuln.id}</td>
                <td>${vuln.vulnerability_type}</td>
                <td><span class="severity-badge severity-${vuln.severity.toLowerCase()}">${vuln.severity}</span></td>
                <td>${vuln.description}</td>
                <td><a href="${vuln.url}" target="_blank" style="color: var(--primary-color);">${this.truncateUrl(vuln.url)}</a></td>
                <td>${vuln.username || 'غير معروف'}</td>
                <td>${new Date(vuln.created_at).toLocaleDateString('ar-SA')}</td>
                <td>
                    <button class="action-btn action-btn-view" onclick="adminPanel.viewVulnerability(${vuln.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    truncateUrl(url) {
        return url.length > 50 ? url.substring(0, 50) + '...' : url;
    }

    // Filter table by search term
    filterTable(tableBodyId, searchTerm) {
        const tbody = document.getElementById(tableBodyId);
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const shouldShow = text.includes(searchTerm.toLowerCase());
            row.style.display = shouldShow ? '' : 'none';
        });
    }

    // Filter table by specific column value
    filterTableByColumn(tableBodyId, columnIndex, filterValue) {
        const tbody = document.getElementById(tableBodyId);
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length > columnIndex) {
                const cellText = cells[columnIndex].textContent.toLowerCase();
                const shouldShow = !filterValue || cellText.includes(filterValue.toLowerCase());
                row.style.display = shouldShow ? '' : 'none';
            }
        });
    }

    showAlert(message, type = 'info') {
        const alertSystem = document.getElementById('alertSystem');
        if (!alertSystem) return;

        const alert = document.createElement('div');
        alert.className = `alert ${type}`;
        alert.innerHTML = `
            <i class="fas fa-${this.getAlertIcon(type)}"></i>
            <span>${message}</span>
        `;

        alertSystem.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.animation = 'slideOut 0.3s ease forwards';
                setTimeout(() => alert.remove(), 300);
            }
        }, 5000);
    }

    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Global functions for admin actions
async function logout() {
    try {
        const response = await fetch('/api/auth/logout', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            localStorage.removeItem('user');
            window.location.href = '/';
        }
    } catch (error) {
        console.error('Logout error:', error);
        window.location.href = '/';
    }
}

function showAddUserModal() {
    document.getElementById('addUserModal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

async function addUser() {
    const username = document.getElementById('newUsername').value;
    const email = document.getElementById('newEmail').value;
    const password = document.getElementById('newPassword').value;
    const role = document.getElementById('newRole').value;

    if (!username || !password) {
        adminPanel.showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
        return;
    }

    try {
        const response = await fetch('/api/auth/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username,
                email,
                password,
                role
            })
        });

        const data = await response.json();

        if (data.success) {
            adminPanel.showAlert('تم إضافة المستخدم بنجاح', 'success');
            closeModal('addUserModal');
            adminPanel.loadUsers();
            
            // Clear form
            document.getElementById('addUserForm').reset();
        } else {
            adminPanel.showAlert(data.message || 'فشل في إضافة المستخدم', 'error');
        }

    } catch (error) {
        console.error('Add user error:', error);
        adminPanel.showAlert('حدث خطأ في إضافة المستخدم', 'error');
    }
}

function generateSystemReport() {
    adminPanel.showAlert('ميزة تقرير النظام قيد التطوير', 'info');
}

function saveSettings() {
    adminPanel.showAlert('تم حفظ الإعدادات بنجاح', 'success');
}

function resetSettings() {
    adminPanel.showAlert('تم إعادة تعيين الإعدادات', 'info');
}

function createBackup() {
    adminPanel.showAlert('تم إنشاء النسخة الاحتياطية', 'success');
}

// Initialize admin panel when DOM is loaded
let adminPanel;
document.addEventListener('DOMContentLoaded', () => {
    adminPanel = new AdminPanel();
    
    // Close modals when clicking outside
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            e.target.style.display = 'none';
        }
    });
});
