// Authentication System for CyberWeb Hunter

class AuthSystem {
    constructor() {
        this.loginForm = document.getElementById('loginForm');
        this.loadingOverlay = document.getElementById('loadingOverlay');
        this.alertSystem = document.getElementById('alertSystem');
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthStatus();
    }

    setupEventListeners() {
        if (this.loginForm) {
            this.loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        // Auto-fill demo credentials on click
        const demoCredentials = document.querySelector('.credential-item');
        if (demoCredentials) {
            demoCredentials.addEventListener('click', () => {
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = 'JaMa<PERSON>@123';
                this.addInputAnimation();
            });
        }

        // Add input focus animations
        const inputs = document.querySelectorAll('.input-group input');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('focused');
            });
            
            input.addEventListener('blur', () => {
                if (!input.value) {
                    input.parentElement.classList.remove('focused');
                }
            });
        });
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        const rememberMe = document.getElementById('rememberMe').checked;

        // Validation
        if (!username || !password) {
            this.showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username,
                    password,
                    rememberMe
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('تم تسجيل الدخول بنجاح!', 'success');
                
                // Store user data
                localStorage.setItem('user', JSON.stringify(data.user));
                
                // Redirect based on role
                setTimeout(() => {
                    if (data.user.role === 'admin') {
                        window.location.href = '/admin';
                    } else {
                        window.location.href = '/dashboard';
                    }
                }, 1500);
                
            } else {
                this.showAlert(data.message || 'فشل في تسجيل الدخول', 'error');
            }

        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('حدث خطأ في الاتصال بالخادم', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async checkAuthStatus() {
        try {
            const response = await fetch('/api/auth/status');
            const data = await response.json();

            if (data.success && data.authenticated) {
                // User is already logged in, redirect to appropriate page
                if (data.user.role === 'admin') {
                    window.location.href = '/admin';
                } else {
                    window.location.href = '/dashboard';
                }
            }
        } catch (error) {
            console.error('Auth status check error:', error);
        }
    }

    showLoading(show) {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = show ? 'flex' : 'none';
        }
    }

    showAlert(message, type = 'info') {
        if (!this.alertSystem) return;

        const alert = document.createElement('div');
        alert.className = `alert ${type}`;
        alert.innerHTML = `
            <i class="fas fa-${this.getAlertIcon(type)}"></i>
            <span>${message}</span>
        `;

        this.alertSystem.appendChild(alert);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.animation = 'slideOut 0.3s ease forwards';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }
        }, 5000);

        // Add click to dismiss
        alert.addEventListener('click', () => {
            alert.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => {
                alert.remove();
            }, 300);
        });
    }

    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    addInputAnimation() {
        const inputs = document.querySelectorAll('.input-group input');
        inputs.forEach((input, index) => {
            setTimeout(() => {
                input.style.transform = 'scale(1.05)';
                input.style.borderColor = 'var(--primary-color)';
                setTimeout(() => {
                    input.style.transform = 'scale(1)';
                    input.style.borderColor = 'var(--border-color)';
                }, 200);
            }, index * 100);
        });
    }
}

// Password toggle functionality
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleBtn = document.querySelector('.toggle-password i');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleBtn.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        toggleBtn.className = 'fas fa-eye';
    }
}

// Cyber typing effect for inputs
function addCyberTypingEffect() {
    const inputs = document.querySelectorAll('.input-group input');
    
    inputs.forEach(input => {
        input.addEventListener('input', (e) => {
            const value = e.target.value;
            const lastChar = value[value.length - 1];
            
            if (lastChar) {
                // Add brief glow effect
                e.target.style.boxShadow = '0 0 20px rgba(0, 255, 65, 0.5)';
                setTimeout(() => {
                    e.target.style.boxShadow = '';
                }, 100);
            }
        });
    });
}

// Keyboard shortcuts
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl + Enter to submit form
        if (e.ctrlKey && e.key === 'Enter') {
            const loginForm = document.getElementById('loginForm');
            if (loginForm) {
                loginForm.dispatchEvent(new Event('submit'));
            }
        }
        
        // Escape to clear form
        if (e.key === 'Escape') {
            const inputs = document.querySelectorAll('.input-group input');
            inputs.forEach(input => {
                input.value = '';
                input.blur();
            });
        }
    });
}

// Add CSS for slideOut animation
const style = document.createElement('style');
style.textContent = `
    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .input-group.focused input {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 20px rgba(0, 255, 65, 0.3) !important;
    }
    
    .login-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const authSystem = new AuthSystem();
    addCyberTypingEffect();
    setupKeyboardShortcuts();
    
    // Add some cyber effects
    setTimeout(() => {
        const loginBox = document.querySelector('.login-box');
        if (loginBox) {
            loginBox.style.animation = 'pulse 3s infinite';
        }
    }, 2000);
});

// Export for use in other modules
window.AuthSystem = AuthSystem;
