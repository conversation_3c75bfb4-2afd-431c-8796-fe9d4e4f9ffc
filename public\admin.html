<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم المسؤول - CyberWeb Hunter</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Matrix Background -->
    <div class="matrix-bg">
        <canvas id="matrix-canvas"></canvas>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-shield-alt"></i>
                <span>CyberWeb Hunter</span>
                <span class="admin-badge">ADMIN</span>
            </div>
            <div class="nav-menu">
                <a href="#overview" class="nav-link active" data-section="overview">
                    <i class="fas fa-chart-pie"></i>
                    <span>نظرة عامة</span>
                </a>
                <a href="#users" class="nav-link" data-section="users">
                    <i class="fas fa-users"></i>
                    <span>المستخدمون</span>
                </a>
                <a href="#scans" class="nav-link" data-section="scans">
                    <i class="fas fa-search"></i>
                    <span>عمليات الفحص</span>
                </a>
                <a href="#vulnerabilities" class="nav-link" data-section="vulnerabilities">
                    <i class="fas fa-bug"></i>
                    <span>الثغرات</span>
                </a>
                <a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-file-pdf"></i>
                    <span>التقارير</span>
                </a>
                <a href="#settings" class="nav-link" data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span id="adminUsername">المسؤول</span>
                    <i class="fas fa-user-shield"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Overview Section -->
        <section id="overview-section" class="content-section active">
            <div class="section-header">
                <h1><i class="fas fa-chart-pie"></i> نظرة عامة على النظام</h1>
                <p>إحصائيات شاملة حول استخدام النظام والأمان</p>
            </div>

            <div class="admin-stats-grid">
                <div class="admin-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalUsers">0</h3>
                        <p>إجمالي المستخدمين</p>
                        <small class="stat-change">+5 هذا الأسبوع</small>
                    </div>
                </div>
                <div class="admin-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalScans">0</h3>
                        <p>عمليات الفحص</p>
                        <small class="stat-change">+12 اليوم</small>
                    </div>
                </div>
                <div class="admin-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-bug"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalVulnerabilities">0</h3>
                        <p>الثغرات المكتشفة</p>
                        <small class="stat-change">+8 اليوم</small>
                    </div>
                </div>
                <div class="admin-stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="completedScans">0</h3>
                        <p>فحوصات مكتملة</p>
                        <small class="stat-change">95% معدل النجاح</small>
                    </div>
                </div>
            </div>

            <div class="admin-dashboard-grid">
                <div class="admin-card">
                    <h3><i class="fas fa-chart-line"></i> إحصائيات الاستخدام</h3>
                    <div class="chart-container">
                        <canvas id="usageChart"></canvas>
                    </div>
                </div>

                <div class="admin-card">
                    <h3><i class="fas fa-shield-virus"></i> أنواع الثغرات</h3>
                    <div class="vulnerability-types">
                        <div class="vuln-type-item">
                            <span class="vuln-type-name">SQL Injection</span>
                            <span class="vuln-type-count">45%</span>
                            <div class="vuln-type-bar">
                                <div class="vuln-type-fill" style="width: 45%; background: #ff0040;"></div>
                            </div>
                        </div>
                        <div class="vuln-type-item">
                            <span class="vuln-type-name">XSS</span>
                            <span class="vuln-type-count">30%</span>
                            <div class="vuln-type-bar">
                                <div class="vuln-type-fill" style="width: 30%; background: #ffaa00;"></div>
                            </div>
                        </div>
                        <div class="vuln-type-item">
                            <span class="vuln-type-name">SSL Issues</span>
                            <span class="vuln-type-count">15%</span>
                            <div class="vuln-type-bar">
                                <div class="vuln-type-fill" style="width: 15%; background: #00ff41;"></div>
                            </div>
                        </div>
                        <div class="vuln-type-item">
                            <span class="vuln-type-name">Other</span>
                            <span class="vuln-type-count">10%</span>
                            <div class="vuln-type-bar">
                                <div class="vuln-type-fill" style="width: 10%; background: #17a2b8;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="admin-card">
                    <h3><i class="fas fa-clock"></i> النشاط الأخير</h3>
                    <div id="recentActivity" class="activity-list">
                        <div class="loading">جاري تحميل النشاط...</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Users Section -->
        <section id="users-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-users"></i> إدارة المستخدمين</h1>
                <p>عرض وإدارة جميع مستخدمي النظام</p>
            </div>

            <div class="admin-controls">
                <button class="btn btn-primary" onclick="showAddUserModal()">
                    <i class="fas fa-plus"></i> إضافة مستخدم جديد
                </button>
                <div class="search-box">
                    <input type="text" id="userSearch" placeholder="البحث عن مستخدم...">
                    <i class="fas fa-search"></i>
                </div>
            </div>

            <div class="admin-table-container">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>المعرف</th>
                            <th>اسم المستخدم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>تاريخ التسجيل</th>
                            <th>آخر دخول</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <tr>
                            <td colspan="8" class="loading">جاري تحميل المستخدمين...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Scans Section -->
        <section id="scans-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-search"></i> عمليات الفحص</h1>
                <p>مراقبة جميع عمليات الفحص في النظام</p>
            </div>

            <div class="admin-controls">
                <div class="filter-controls">
                    <select id="scanStatusFilter">
                        <option value="">جميع الحالات</option>
                        <option value="completed">مكتمل</option>
                        <option value="running">قيد التشغيل</option>
                        <option value="failed">فاشل</option>
                        <option value="stopped">متوقف</option>
                    </select>
                    <input type="date" id="scanDateFilter">
                </div>
                <div class="search-box">
                    <input type="text" id="scanSearch" placeholder="البحث في عمليات الفحص...">
                    <i class="fas fa-search"></i>
                </div>
            </div>

            <div class="admin-table-container">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>المعرف</th>
                            <th>المستخدم</th>
                            <th>الموقع المستهدف</th>
                            <th>نوع الفحص</th>
                            <th>الحالة</th>
                            <th>تاريخ البدء</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الثغرات</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="scansTableBody">
                        <tr>
                            <td colspan="9" class="loading">جاري تحميل عمليات الفحص...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Vulnerabilities Section -->
        <section id="vulnerabilities-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-bug"></i> الثغرات المكتشفة</h1>
                <p>عرض جميع الثغرات الأمنية المكتشفة في النظام</p>
            </div>

            <div class="admin-controls">
                <div class="filter-controls">
                    <select id="vulnSeverityFilter">
                        <option value="">جميع المستويات</option>
                        <option value="High">عالي</option>
                        <option value="Medium">متوسط</option>
                        <option value="Low">منخفض</option>
                    </select>
                    <select id="vulnTypeFilter">
                        <option value="">جميع الأنواع</option>
                        <option value="SQL Injection">SQL Injection</option>
                        <option value="XSS">XSS</option>
                        <option value="CSRF">CSRF</option>
                        <option value="SSL">SSL/TLS</option>
                    </select>
                </div>
                <div class="search-box">
                    <input type="text" id="vulnSearch" placeholder="البحث في الثغرات...">
                    <i class="fas fa-search"></i>
                </div>
            </div>

            <div class="admin-table-container">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>المعرف</th>
                            <th>النوع</th>
                            <th>الخطورة</th>
                            <th>الوصف</th>
                            <th>الرابط</th>
                            <th>المستخدم</th>
                            <th>تاريخ الاكتشاف</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="vulnerabilitiesTableBody">
                        <tr>
                            <td colspan="8" class="loading">جاري تحميل الثغرات...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-file-pdf"></i> إدارة التقارير</h1>
                <p>عرض وإدارة جميع التقارير المُنشأة</p>
            </div>

            <div class="admin-controls">
                <button class="btn btn-success" onclick="generateSystemReport()">
                    <i class="fas fa-chart-bar"></i> إنشاء تقرير النظام
                </button>
                <div class="search-box">
                    <input type="text" id="reportSearch" placeholder="البحث في التقارير...">
                    <i class="fas fa-search"></i>
                </div>
            </div>

            <div class="reports-grid" id="reportsGrid">
                <div class="loading">جاري تحميل التقارير...</div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-cog"></i> إعدادات النظام</h1>
                <p>تكوين إعدادات النظام والأمان</p>
            </div>

            <div class="settings-grid">
                <div class="settings-card">
                    <h3><i class="fas fa-shield-alt"></i> إعدادات الأمان</h3>
                    <div class="setting-item">
                        <label>مدة انتهاء الجلسة (دقيقة)</label>
                        <input type="number" id="sessionTimeout" value="60" min="5" max="1440">
                    </div>
                    <div class="setting-item">
                        <label>الحد الأقصى لمحاولات تسجيل الدخول</label>
                        <input type="number" id="maxLoginAttempts" value="5" min="1" max="20">
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableTwoFactor">
                            <span class="checkmark"></span>
                            تفعيل المصادقة الثنائية
                        </label>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><i class="fas fa-scan"></i> إعدادات الفحص</h3>
                    <div class="setting-item">
                        <label>الحد الأقصى للفحوصات المتزامنة</label>
                        <input type="number" id="maxConcurrentScans" value="10" min="1" max="50">
                    </div>
                    <div class="setting-item">
                        <label>مهلة انتظار الفحص (ثانية)</label>
                        <input type="number" id="scanTimeout" value="300" min="30" max="3600">
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableScanLogging">
                            <span class="checkmark"></span>
                            تسجيل تفاصيل الفحص
                        </label>
                    </div>
                </div>

                <div class="settings-card">
                    <h3><i class="fas fa-database"></i> إعدادات قاعدة البيانات</h3>
                    <div class="setting-item">
                        <label>فترة الاحتفاظ بالبيانات (أيام)</label>
                        <input type="number" id="dataRetention" value="90" min="7" max="365">
                    </div>
                    <div class="setting-item">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableAutoBackup">
                            <span class="checkmark"></span>
                            النسخ الاحتياطي التلقائي
                        </label>
                    </div>
                    <button class="btn btn-info" onclick="createBackup()">
                        <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                    </button>
                </div>
            </div>

            <div class="settings-actions">
                <button class="btn btn-primary" onclick="saveSettings()">
                    <i class="fas fa-save"></i> حفظ الإعدادات
                </button>
                <button class="btn btn-warning" onclick="resetSettings()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <div id="addUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> إضافة مستخدم جديد</h3>
                <button class="modal-close" onclick="closeModal('addUserModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="form-group">
                        <label for="newUsername">اسم المستخدم</label>
                        <input type="text" id="newUsername" required>
                    </div>
                    <div class="form-group">
                        <label for="newEmail">البريد الإلكتروني</label>
                        <input type="email" id="newEmail">
                    </div>
                    <div class="form-group">
                        <label for="newPassword">كلمة المرور</label>
                        <input type="password" id="newPassword" required>
                    </div>
                    <div class="form-group">
                        <label for="newRole">الدور</label>
                        <select id="newRole">
                            <option value="user">مستخدم</option>
                            <option value="admin">مسؤول</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="addUser()">
                    <i class="fas fa-plus"></i> إضافة
                </button>
                <button class="btn btn-secondary" onclick="closeModal('addUserModal')">
                    إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- Alert System -->
    <div id="alertSystem" class="alert-system"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>جاري المعالجة...</p>
        </div>
    </div>

    <script src="js/matrix.js"></script>
    <script src="js/admin.js"></script>
</body>
</html>
