// Dashboard functionality for CyberWeb Hunter

class Dashboard {
    constructor() {
        this.currentUser = null;
        this.currentScanId = null;
        this.scanInterval = null;
        
        this.init();
    }

    async init() {
        await this.checkAuth();
        this.setupEventListeners();
        this.setupNavigation();
        await this.loadDashboardData();
    }

    async checkAuth() {
        try {
            const response = await fetch('/api/auth/status');
            const data = await response.json();

            if (!data.success || !data.authenticated) {
                window.location.href = '/';
                return;
            }

            this.currentUser = data.user;
            document.getElementById('username').textContent = data.user.username;

        } catch (error) {
            console.error('Auth check error:', error);
            window.location.href = '/';
        }
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = link.dataset.section;
                this.showSection(section);
            });
        });

        // Quick scan form
        const quickScanForm = document.querySelector('.quick-scan-form');
        if (quickScanForm) {
            quickScanForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.startQuickScan();
            });
        }
    }

    setupNavigation() {
        // Set active navigation based on hash
        const hash = window.location.hash.substring(1) || 'dashboard';
        this.showSection(hash);

        // Handle browser back/forward
        window.addEventListener('hashchange', () => {
            const section = window.location.hash.substring(1) || 'dashboard';
            this.showSection(section);
        });
    }

    showSection(sectionName) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`)?.classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${sectionName}-section`)?.classList.add('active');

        // Update URL
        window.location.hash = sectionName;

        // Load section-specific data
        this.loadSectionData(sectionName);
    }

    async loadSectionData(sectionName) {
        switch (sectionName) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'history':
                await this.loadScanHistory();
                break;
            case 'reports':
                await this.loadReports();
                break;
        }
    }

    async loadDashboardData() {
        try {
            // Load user statistics
            const response = await fetch('/api/scan/history?limit=5');
            const data = await response.json();

            if (data.success) {
                // Update stats
                document.getElementById('totalScans').textContent = data.pagination?.total || 0;
                
                // Calculate vulnerabilities
                let totalVulns = 0;
                data.sessions.forEach(session => {
                    totalVulns += session.vulnerability_count || 0;
                });
                document.getElementById('totalVulns').textContent = totalVulns;

                // Update last scan
                if (data.sessions.length > 0) {
                    const lastScan = new Date(data.sessions[0].created_at);
                    document.getElementById('lastScan').textContent = lastScan.toLocaleDateString('ar-SA');
                }

                // Update recent results
                this.updateRecentResults(data.sessions);
            }

        } catch (error) {
            console.error('Load dashboard data error:', error);
        }
    }

    updateRecentResults(sessions) {
        const container = document.getElementById('recentResults');
        
        if (sessions.length === 0) {
            container.innerHTML = '<p class="no-data">لا توجد نتائج حديثة</p>';
            return;
        }

        container.innerHTML = sessions.map(session => `
            <div class="result-item" style="padding: 1rem; border-bottom: 1px solid var(--border-color);">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <strong>${session.target_url}</strong>
                        <br>
                        <small style="color: var(--text-secondary);">
                            ${new Date(session.created_at).toLocaleDateString('ar-SA')}
                        </small>
                    </div>
                    <div style="text-align: left;">
                        <span class="status-badge status-${session.status}">${this.getStatusText(session.status)}</span>
                        <br>
                        <small style="color: var(--primary-color);">
                            ${session.vulnerability_count || 0} ثغرة
                        </small>
                    </div>
                </div>
            </div>
        `).join('');
    }

    getStatusText(status) {
        const statusMap = {
            'completed': 'مكتمل',
            'running': 'قيد التشغيل',
            'failed': 'فاشل',
            'stopped': 'متوقف'
        };
        return statusMap[status] || status;
    }

    async startQuickScan() {
        const url = document.getElementById('quickScanUrl').value.trim();
        
        if (!url) {
            this.showAlert('يرجى إدخال رابط الموقع', 'error');
            return;
        }

        try {
            new URL(url); // Validate URL
        } catch (error) {
            this.showAlert('رابط غير صحيح', 'error');
            return;
        }

        this.showLoading(true);

        try {
            const response = await fetch('/api/scan/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    target_url: url,
                    scan_types: ['sql_injection', 'xss', 'ssl_check']
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('تم بدء الفحص بنجاح!', 'success');
                this.currentScanId = data.sessionId;
                
                // Switch to scanner section and show progress
                this.showSection('scanner');
                this.showScanProgress();
                this.startScanMonitoring();
                
                // Clear the input
                document.getElementById('quickScanUrl').value = '';
                
            } else {
                this.showAlert(data.message || 'فشل في بدء الفحص', 'error');
            }

        } catch (error) {
            console.error('Quick scan error:', error);
            this.showAlert('حدث خطأ في بدء الفحص', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async startAdvancedScan() {
        const targetUrl = document.getElementById('targetUrl').value.trim();
        
        if (!targetUrl) {
            this.showAlert('يرجى إدخال رابط الموقع', 'error');
            return;
        }

        // Get selected scan types
        const scanTypes = [];
        document.querySelectorAll('.scan-types input[type="checkbox"]:checked').forEach(checkbox => {
            scanTypes.push(checkbox.value);
        });

        if (scanTypes.length === 0) {
            this.showAlert('يرجى اختيار نوع فحص واحد على الأقل', 'error');
            return;
        }

        // Get advanced options
        const options = {
            useProxy: document.getElementById('useProxy').checked,
            stealthMode: document.getElementById('stealthMode').checked,
            deepScan: document.getElementById('deepScan').checked
        };

        this.showLoading(true);

        try {
            const response = await fetch('/api/scan/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    target_url: targetUrl,
                    scan_types: scanTypes,
                    options
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('تم بدء الفحص المتقدم بنجاح!', 'success');
                this.currentScanId = data.sessionId;
                
                this.showScanProgress();
                this.startScanMonitoring();
                
            } else {
                this.showAlert(data.message || 'فشل في بدء الفحص', 'error');
            }

        } catch (error) {
            console.error('Advanced scan error:', error);
            this.showAlert('حدث خطأ في بدء الفحص', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    showScanProgress() {
        document.querySelector('.scan-config').style.display = 'none';
        document.getElementById('scanProgress').style.display = 'block';
        document.getElementById('scanResults').style.display = 'none';
    }

    startScanMonitoring() {
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
        }

        this.scanInterval = setInterval(async () => {
            await this.checkScanStatus();
        }, 2000);
    }

    async checkScanStatus() {
        if (!this.currentScanId) return;

        try {
            const response = await fetch(`/api/scan/status/${this.currentScanId}`);
            const data = await response.json();

            if (data.success) {
                const session = data.session;
                
                // Update progress
                this.updateScanProgress(session);

                if (session.status === 'completed' || session.status === 'failed' || session.status === 'stopped') {
                    clearInterval(this.scanInterval);
                    this.showScanResults(session);
                }
            }

        } catch (error) {
            console.error('Check scan status error:', error);
        }
    }

    updateScanProgress(session) {
        const statusElement = document.getElementById('scanStatus');
        const progressElement = document.getElementById('progressFill');

        let progress = 0;
        let statusText = 'جاري التحضير...';

        switch (session.status) {
            case 'running':
                progress = 50;
                statusText = 'جاري فحص الموقع...';
                break;
            case 'completed':
                progress = 100;
                statusText = 'تم الانتهاء من الفحص';
                break;
            case 'failed':
                progress = 100;
                statusText = 'فشل الفحص';
                break;
            case 'stopped':
                progress = 100;
                statusText = 'تم إيقاف الفحص';
                break;
        }

        progressElement.style.width = `${progress}%`;
        statusElement.textContent = statusText;
    }

    showScanResults(session) {
        document.getElementById('scanProgress').style.display = 'none';
        document.getElementById('scanResults').style.display = 'block';

        const resultsContainer = document.getElementById('resultsContent');
        
        if (session.vulnerabilities && session.vulnerabilities.length > 0) {
            resultsContainer.innerHTML = `
                <div class="results-summary">
                    <h4>تم العثور على ${session.vulnerabilities.length} ثغرة أمنية</h4>
                </div>
                <div class="vulnerabilities-list">
                    ${session.vulnerabilities.map(vuln => `
                        <div class="vulnerability-item" style="padding: 1rem; margin: 1rem 0; background: rgba(255,255,255,0.05); border-radius: 8px; border-right: 4px solid ${this.getSeverityColor(vuln.severity)};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.5rem;">
                                <strong>${vuln.vulnerability_type}</strong>
                                <span class="severity-badge" style="background: ${this.getSeverityColor(vuln.severity)}; color: white; padding: 0.25rem 0.75rem; border-radius: 15px; font-size: 0.8rem;">
                                    ${vuln.severity}
                                </span>
                            </div>
                            <p style="color: var(--text-secondary); margin-bottom: 0.5rem;">${vuln.description}</p>
                            <small style="color: var(--text-secondary);">URL: ${vuln.url}</small>
                        </div>
                    `).join('')}
                </div>
            `;
        } else {
            resultsContainer.innerHTML = `
                <div class="no-vulnerabilities" style="text-align: center; padding: 2rem; color: var(--success-color);">
                    <i class="fas fa-shield-check" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                    <h4>لم يتم العثور على أي ثغرات أمنية!</h4>
                    <p>الموقع يبدو آمناً من الثغرات المفحوصة.</p>
                </div>
            `;
        }
    }

    getSeverityColor(severity) {
        const colors = {
            'High': '#ff0040',
            'Medium': '#ffaa00',
            'Low': '#00ff41'
        };
        return colors[severity] || '#666';
    }

    async stopScan() {
        if (!this.currentScanId) return;

        try {
            const response = await fetch(`/api/scan/stop/${this.currentScanId}`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('تم إيقاف الفحص', 'info');
                clearInterval(this.scanInterval);
            }

        } catch (error) {
            console.error('Stop scan error:', error);
            this.showAlert('فشل في إيقاف الفحص', 'error');
        }
    }

    async generateReport() {
        if (!this.currentScanId) return;

        this.showLoading(true);

        try {
            const response = await fetch(`/api/reports/generate/${this.currentScanId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    format: 'pdf',
                    includeDetails: true
                })
            });

            const data = await response.json();

            if (data.success) {
                this.showAlert('تم إنشاء التقرير بنجاح!', 'success');
                
                // Download the report
                window.open(data.reportPath, '_blank');
                
            } else {
                this.showAlert(data.message || 'فشل في إنشاء التقرير', 'error');
            }

        } catch (error) {
            console.error('Generate report error:', error);
            this.showAlert('حدث خطأ في إنشاء التقرير', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async loadScanHistory() {
        try {
            const response = await fetch('/api/scan/history');
            const data = await response.json();

            if (data.success) {
                this.displayScanHistory(data.sessions);
            }

        } catch (error) {
            console.error('Load scan history error:', error);
        }
    }

    displayScanHistory(sessions) {
        const container = document.getElementById('historyList');
        
        if (sessions.length === 0) {
            container.innerHTML = '<div class="no-data">لا توجد عمليات فحص سابقة</div>';
            return;
        }

        container.innerHTML = sessions.map(session => `
            <div class="history-item" style="padding: 1.5rem; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h4 style="color: var(--text-primary); margin-bottom: 0.5rem;">${session.target_url}</h4>
                    <p style="color: var(--text-secondary); margin-bottom: 0.5rem;">
                        ${new Date(session.created_at).toLocaleDateString('ar-SA')} - 
                        ${session.vulnerability_count || 0} ثغرة مكتشفة
                    </p>
                    <span class="status-badge status-${session.status}">${this.getStatusText(session.status)}</span>
                </div>
                <div style="display: flex; gap: 0.5rem;">
                    <button onclick="dashboard.viewScanDetails(${session.id})" class="btn btn-info" style="padding: 0.5rem 1rem;">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                    <button onclick="dashboard.deleteScan(${session.id})" class="btn btn-danger" style="padding: 0.5rem 1rem;">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    async loadReports() {
        try {
            const response = await fetch('/api/reports/list');
            const data = await response.json();

            if (data.success) {
                this.displayReports(data.reports);
            }

        } catch (error) {
            console.error('Load reports error:', error);
        }
    }

    displayReports(reports) {
        const container = document.getElementById('reportsList');
        
        if (reports.length === 0) {
            container.innerHTML = '<div class="no-data">لا توجد تقارير</div>';
            return;
        }

        container.innerHTML = reports.map(report => `
            <div class="report-item" style="padding: 1.5rem; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center;">
                <div>
                    <h4 style="color: var(--text-primary); margin-bottom: 0.5rem;">${report.target_url}</h4>
                    <p style="color: var(--text-secondary);">
                        ${new Date(report.created_at).toLocaleDateString('ar-SA')} - 
                        ${report.report_type.toUpperCase()}
                    </p>
                </div>
                <div style="display: flex; gap: 0.5rem;">
                    <button onclick="dashboard.downloadReport('${report.file_path}')" class="btn btn-success" style="padding: 0.5rem 1rem;">
                        <i class="fas fa-download"></i> تحميل
                    </button>
                    <button onclick="dashboard.deleteReport(${report.id})" class="btn btn-danger" style="padding: 0.5rem 1rem;">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `).join('');
    }

    showLoading(show) {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.style.display = show ? 'flex' : 'none';
        }
    }

    showAlert(message, type = 'info') {
        const alertSystem = document.getElementById('alertSystem');
        if (!alertSystem) return;

        const alert = document.createElement('div');
        alert.className = `alert ${type}`;
        alert.innerHTML = `
            <i class="fas fa-${this.getAlertIcon(type)}"></i>
            <span>${message}</span>
        `;

        alertSystem.appendChild(alert);

        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.animation = 'slideOut 0.3s ease forwards';
                setTimeout(() => alert.remove(), 300);
            }
        }, 5000);

        alert.addEventListener('click', () => {
            alert.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => alert.remove(), 300);
        });
    }

    getAlertIcon(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-triangle',
            warning: 'exclamation-circle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    }
}

// Global functions
async function logout() {
    try {
        const response = await fetch('/api/auth/logout', { method: 'POST' });
        const data = await response.json();

        if (data.success) {
            localStorage.removeItem('user');
            window.location.href = '/';
        }
    } catch (error) {
        console.error('Logout error:', error);
        window.location.href = '/';
    }
}

function startQuickScan() {
    dashboard.startQuickScan();
}

function startAdvancedScan() {
    dashboard.startAdvancedScan();
}

function stopScan() {
    dashboard.stopScan();
}

function generateReport() {
    dashboard.generateReport();
}

function exportResults() {
    dashboard.showAlert('ميزة التصدير قيد التطوير', 'info');
}

// Initialize dashboard when DOM is loaded
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new Dashboard();
    
    // Add status badge styles
    const style = document.createElement('style');
    style.textContent = `
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        .status-completed { background: var(--success-color); color: var(--dark-bg); }
        .status-running { background: var(--warning-color); color: var(--dark-bg); }
        .status-failed { background: var(--danger-color); color: white; }
        .status-stopped { background: var(--text-secondary); color: white; }
    `;
    document.head.appendChild(style);
});
