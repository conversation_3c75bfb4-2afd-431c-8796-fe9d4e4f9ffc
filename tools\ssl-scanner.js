// SSL/TLS Security Scanner for CyberWeb Hunter

const https = require('https');
const tls = require('tls');
const { URL } = require('url');

class SSLScanner {
    constructor() {
        this.vulnerabilities = [];
    }

    // Main SSL/TLS scanning function
    async scanSSL(targetUrl) {
        console.log(`🔒 Starting SSL/TLS scan for: ${targetUrl}`);
        
        try {
            const url = new URL(targetUrl);
            
            if (url.protocol !== 'https:') {
                this.vulnerabilities.push({
                    type: 'SSL/TLS Configuration',
                    severity: 'Medium',
                    url: targetUrl,
                    payload: 'N/A',
                    description: 'Website does not use HTTPS',
                    evidence: 'HTTP protocol detected instead of HTTPS',
                    recommendation: 'Implement HTTPS with a valid SSL/TLS certificate'
                });
                return this.vulnerabilities;
            }

            const hostname = url.hostname;
            const port = url.port || 443;

            // Get certificate information
            const certInfo = await this.getCertificateInfo(hostname, port);
            
            // Check certificate validity
            await this.checkCertificateValidity(certInfo, hostname);
            
            // Check SSL/TLS configuration
            await this.checkSSLConfiguration(hostname, port);
            
            // Check for security headers
            await this.checkSecurityHeaders(targetUrl);

        } catch (error) {
            console.error('SSL scan error:', error.message);
            this.vulnerabilities.push({
                type: 'SSL/TLS Configuration',
                severity: 'High',
                url: targetUrl,
                payload: 'N/A',
                description: 'SSL/TLS connection failed',
                evidence: error.message,
                recommendation: 'Check SSL/TLS configuration and certificate validity'
            });
        }

        console.log(`✅ SSL/TLS scan completed. Found ${this.vulnerabilities.length} potential issues.`);
        return this.vulnerabilities;
    }

    // Get certificate information
    getCertificateInfo(hostname, port) {
        return new Promise((resolve, reject) => {
            const options = {
                host: hostname,
                port: port,
                method: 'GET',
                rejectUnauthorized: false, // Allow self-signed certificates for analysis
                agent: false
            };

            const req = https.request(options, (res) => {
                const cert = res.socket.getPeerCertificate(true);
                resolve(cert);
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.setTimeout(10000, () => {
                req.destroy();
                reject(new Error('SSL connection timeout'));
            });

            req.end();
        });
    }

    // Check certificate validity
    async checkCertificateValidity(cert, hostname) {
        if (!cert || Object.keys(cert).length === 0) {
            this.vulnerabilities.push({
                type: 'SSL Certificate',
                severity: 'High',
                url: hostname,
                payload: 'N/A',
                description: 'No SSL certificate found',
                evidence: 'Certificate information not available',
                recommendation: 'Install a valid SSL certificate'
            });
            return;
        }

        // Check certificate expiration
        const now = new Date();
        const validFrom = new Date(cert.valid_from);
        const validTo = new Date(cert.valid_to);

        if (now < validFrom) {
            this.vulnerabilities.push({
                type: 'SSL Certificate',
                severity: 'High',
                url: hostname,
                payload: 'N/A',
                description: 'SSL certificate is not yet valid',
                evidence: `Certificate valid from: ${cert.valid_from}`,
                recommendation: 'Check system time or replace certificate'
            });
        }

        if (now > validTo) {
            this.vulnerabilities.push({
                type: 'SSL Certificate',
                severity: 'High',
                url: hostname,
                payload: 'N/A',
                description: 'SSL certificate has expired',
                evidence: `Certificate expired on: ${cert.valid_to}`,
                recommendation: 'Renew SSL certificate immediately'
            });
        }

        // Check if certificate expires soon (within 30 days)
        const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000));
        if (validTo < thirtyDaysFromNow && validTo > now) {
            this.vulnerabilities.push({
                type: 'SSL Certificate',
                severity: 'Medium',
                url: hostname,
                payload: 'N/A',
                description: 'SSL certificate expires soon',
                evidence: `Certificate expires on: ${cert.valid_to}`,
                recommendation: 'Plan certificate renewal'
            });
        }

        // Check self-signed certificate
        if (cert.issuer && cert.subject && 
            JSON.stringify(cert.issuer) === JSON.stringify(cert.subject)) {
            this.vulnerabilities.push({
                type: 'SSL Certificate',
                severity: 'Medium',
                url: hostname,
                payload: 'N/A',
                description: 'Self-signed SSL certificate detected',
                evidence: 'Certificate issuer and subject are identical',
                recommendation: 'Use a certificate from a trusted Certificate Authority'
            });
        }

        // Check hostname mismatch
        if (cert.subject && cert.subject.CN) {
            const certHostname = cert.subject.CN.toLowerCase();
            const targetHostname = hostname.toLowerCase();
            
            if (certHostname !== targetHostname && 
                !certHostname.startsWith('*.') && 
                !this.matchesWildcard(targetHostname, certHostname)) {
                
                this.vulnerabilities.push({
                    type: 'SSL Certificate',
                    severity: 'High',
                    url: hostname,
                    payload: 'N/A',
                    description: 'SSL certificate hostname mismatch',
                    evidence: `Certificate CN: ${certHostname}, Target: ${targetHostname}`,
                    recommendation: 'Use a certificate that matches the domain name'
                });
            }
        }

        // Check weak signature algorithm
        if (cert.signatureAlgorithm) {
            const weakAlgorithms = ['md5', 'sha1'];
            const algorithm = cert.signatureAlgorithm.toLowerCase();
            
            for (const weakAlg of weakAlgorithms) {
                if (algorithm.includes(weakAlg)) {
                    this.vulnerabilities.push({
                        type: 'SSL Certificate',
                        severity: 'Medium',
                        url: hostname,
                        payload: 'N/A',
                        description: 'Weak certificate signature algorithm',
                        evidence: `Signature algorithm: ${cert.signatureAlgorithm}`,
                        recommendation: 'Use a certificate with SHA-256 or stronger signature algorithm'
                    });
                    break;
                }
            }
        }

        // Check key length
        if (cert.bits && cert.bits < 2048) {
            this.vulnerabilities.push({
                type: 'SSL Certificate',
                severity: 'Medium',
                url: hostname,
                payload: 'N/A',
                description: 'Weak SSL certificate key length',
                evidence: `Key length: ${cert.bits} bits`,
                recommendation: 'Use a certificate with at least 2048-bit key length'
            });
        }
    }

    // Check SSL/TLS configuration
    async checkSSLConfiguration(hostname, port) {
        try {
            // Test for weak protocols
            const weakProtocols = ['SSLv2', 'SSLv3', 'TLSv1', 'TLSv1.1'];
            
            for (const protocol of weakProtocols) {
                try {
                    await this.testProtocol(hostname, port, protocol);
                    this.vulnerabilities.push({
                        type: 'SSL/TLS Configuration',
                        severity: protocol.includes('SSL') ? 'High' : 'Medium',
                        url: hostname,
                        payload: 'N/A',
                        description: `Weak SSL/TLS protocol supported: ${protocol}`,
                        evidence: `Server accepts ${protocol} connections`,
                        recommendation: 'Disable weak SSL/TLS protocols and use TLS 1.2 or higher'
                    });
                } catch (error) {
                    // Protocol not supported (good)
                }
            }

            // Check for weak cipher suites
            await this.checkWeakCiphers(hostname, port);

        } catch (error) {
            console.error('SSL configuration check error:', error.message);
        }
    }

    // Test specific SSL/TLS protocol
    testProtocol(hostname, port, protocol) {
        return new Promise((resolve, reject) => {
            const options = {
                host: hostname,
                port: port,
                secureProtocol: this.getSecureProtocol(protocol),
                rejectUnauthorized: false
            };

            const socket = tls.connect(options, () => {
                socket.destroy();
                resolve();
            });

            socket.on('error', (error) => {
                reject(error);
            });

            socket.setTimeout(5000, () => {
                socket.destroy();
                reject(new Error('Protocol test timeout'));
            });
        });
    }

    // Get secure protocol string for Node.js
    getSecureProtocol(protocol) {
        const protocolMap = {
            'SSLv2': 'SSLv2_method',
            'SSLv3': 'SSLv3_method',
            'TLSv1': 'TLSv1_method',
            'TLSv1.1': 'TLSv1_1_method',
            'TLSv1.2': 'TLSv1_2_method',
            'TLSv1.3': 'TLSv1_3_method'
        };
        return protocolMap[protocol] || 'TLSv1_2_method';
    }

    // Check for weak cipher suites
    async checkWeakCiphers(hostname, port) {
        const weakCiphers = [
            'RC4',
            'DES',
            'MD5',
            'NULL',
            'EXPORT',
            'ADH',
            'AECDH'
        ];

        try {
            const socket = tls.connect({
                host: hostname,
                port: port,
                rejectUnauthorized: false
            }, () => {
                const cipher = socket.getCipher();
                if (cipher && cipher.name) {
                    for (const weakCipher of weakCiphers) {
                        if (cipher.name.toUpperCase().includes(weakCipher)) {
                            this.vulnerabilities.push({
                                type: 'SSL/TLS Configuration',
                                severity: 'Medium',
                                url: hostname,
                                payload: 'N/A',
                                description: 'Weak cipher suite detected',
                                evidence: `Cipher: ${cipher.name}`,
                                recommendation: 'Configure server to use strong cipher suites only'
                            });
                            break;
                        }
                    }
                }
                socket.destroy();
            });

            socket.on('error', () => {
                // Ignore errors for this test
            });

        } catch (error) {
            // Ignore errors for cipher testing
        }
    }

    // Check security headers
    async checkSecurityHeaders(targetUrl) {
        try {
            const https = require('https');
            const { URL } = require('url');
            const url = new URL(targetUrl);

            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'HEAD',
                rejectUnauthorized: false
            };

            const response = await new Promise((resolve, reject) => {
                const req = https.request(options, resolve);
                req.on('error', reject);
                req.setTimeout(10000, () => {
                    req.destroy();
                    reject(new Error('Request timeout'));
                });
                req.end();
            });

            const headers = response.headers;

            // Check for missing security headers
            const securityHeaders = {
                'strict-transport-security': 'HTTP Strict Transport Security (HSTS)',
                'x-frame-options': 'X-Frame-Options',
                'x-content-type-options': 'X-Content-Type-Options',
                'x-xss-protection': 'X-XSS-Protection',
                'content-security-policy': 'Content Security Policy',
                'referrer-policy': 'Referrer Policy'
            };

            for (const [header, description] of Object.entries(securityHeaders)) {
                if (!headers[header]) {
                    this.vulnerabilities.push({
                        type: 'Security Headers',
                        severity: 'Low',
                        url: targetUrl,
                        payload: 'N/A',
                        description: `Missing security header: ${description}`,
                        evidence: `${header} header not found`,
                        recommendation: `Implement ${description} header for enhanced security`
                    });
                }
            }

        } catch (error) {
            console.error('Security headers check error:', error.message);
        }
    }

    // Helper function to match wildcard certificates
    matchesWildcard(hostname, certHostname) {
        if (!certHostname.startsWith('*.')) {
            return false;
        }
        
        const domain = certHostname.substring(2);
        return hostname.endsWith('.' + domain) || hostname === domain;
    }

    // Get all vulnerabilities
    getVulnerabilities() {
        return this.vulnerabilities;
    }

    // Clear vulnerabilities
    clearVulnerabilities() {
        this.vulnerabilities = [];
    }
}

module.exports = SSLScanner;
