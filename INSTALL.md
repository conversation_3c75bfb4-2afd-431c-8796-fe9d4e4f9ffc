# 🚀 دليل التثبيت والتشغيل - CyberWeb Hunter

## 📋 المتطلبات الأساسية

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من الموقع الرسمي:
- **Windows**: [تحميل Node.js للويندوز](https://nodejs.org/en/download/)
- **macOS**: [تحميل Node.js للماك](https://nodejs.org/en/download/)
- **Linux**: استخدم مدير الحزم الخاص بتوزيعتك

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nodejs npm

# CentOS/RHEL
sudo yum install nodejs npm

# Arch Linux
sudo pacman -S nodejs npm
```

### 2. التحقق من التثبيت
```bash
node --version    # يجب أن يكون 16.0.0 أو أحدث
npm --version     # يجب أن يكون 8.0.0 أو أحدث
```

## 🔧 خطوات التثبيت

### الطريقة الأولى: التحميل المباشر

1. **تحميل الملفات**
   - قم بتحميل جميع ملفات المشروع
   - استخرج الملفات إلى مجلد `CyperWeB`

2. **فتح Terminal/Command Prompt**
   ```bash
   cd C:\Users\<USER>\Desktop\CyperWeB
   ```

3. **تثبيت التبعيات**
   ```bash
   npm install
   ```

4. **تشغيل الخادم**
   ```bash
   npm start
   ```

### الطريقة الثانية: استخدام Git

1. **استنساخ المستودع**
   ```bash
   git clone https://github.com/cyberweb-hunter/cyberweb-hunter.git
   cd cyberweb-hunter
   ```

2. **تثبيت التبعيات**
   ```bash
   npm install
   ```

3. **تشغيل الخادم**
   ```bash
   npm start
   ```

## 🌐 الوصول للتطبيق

بعد تشغيل الخادم بنجاح، ستظهر رسالة مشابهة لهذه:

```
✅ Database initialized successfully
🚀 CyberWeb Hunter Server running on http://localhost:3000
🔐 Admin Login: admin / JaMaL@123
📊 Dashboard: http://localhost:3000/dashboard
⚙️  Admin Panel: http://localhost:3000/admin
```

### الروابط المهمة:
- **الصفحة الرئيسية**: http://localhost:3000
- **لوحة التحكم**: http://localhost:3000/dashboard
- **لوحة المسؤول**: http://localhost:3000/admin

### بيانات تسجيل الدخول:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `JaMaL@123`

## 🛠️ استكشاف الأخطاء وإصلاحها

### خطأ: "npm is not recognized"
**الحل**: تأكد من تثبيت Node.js بشكل صحيح وإعادة تشغيل Terminal

### خطأ: "EADDRINUSE: address already in use"
**الحل**: المنفذ 3000 مستخدم بالفعل
```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID_NUMBER> /F

# Linux/macOS
lsof -ti:3000 | xargs kill -9
```

### خطأ: "Cannot find module"
**الحل**: تأكد من تثبيت جميع التبعيات
```bash
rm -rf node_modules package-lock.json
npm install
```

### خطأ: "Database error"
**الحل**: تأكد من وجود مجلد database وأذونات الكتابة
```bash
mkdir -p database
chmod 755 database
```

## 🔧 إعدادات متقدمة

### تغيير المنفذ
```bash
# Windows
set PORT=8080 && npm start

# Linux/macOS
PORT=8080 npm start
```

### تشغيل في وضع التطوير
```bash
npm run dev
```

### تشغيل في الخلفية (Linux/macOS)
```bash
nohup npm start > cyberweb.log 2>&1 &
```

### تشغيل كخدمة Windows
استخدم PM2 أو NSSM لتشغيل التطبيق كخدمة Windows

## 📊 مراقبة الأداء

### عرض السجلات
```bash
# عرض السجلات المباشرة
tail -f logs/app.log

# عرض آخر 100 سطر
tail -n 100 logs/app.log
```

### مراقبة استخدام الموارد
```bash
# Linux/macOS
top -p $(pgrep node)

# Windows
tasklist | findstr node
```

## 🔐 إعدادات الأمان

### تغيير كلمة مرور المسؤول
1. سجل دخول كمسؤول
2. انتقل إلى إعدادات النظام
3. غيّر كلمة المرور من لوحة إدارة المستخدمين

### تفعيل HTTPS (للإنتاج)
```javascript
// في ملف server.js
const https = require('https');
const fs = require('fs');

const options = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem')
};

https.createServer(options, app).listen(443);
```

## 📱 الوصول من الأجهزة الأخرى

### في الشبكة المحلية
1. احصل على عنوان IP للجهاز:
   ```bash
   # Windows
   ipconfig
   
   # Linux/macOS
   ifconfig
   ```

2. استخدم العنوان في المتصفح:
   ```
   http://*************:3000
   ```

### عبر الإنترنت (متقدم)
استخدم خدمات مثل ngrok للوصول العام:
```bash
npm install -g ngrok
ngrok http 3000
```

## 🔄 التحديث

### تحديث التبعيات
```bash
npm update
```

### تحديث Node.js
قم بتحميل أحدث إصدار من الموقع الرسمي

## 📞 الحصول على المساعدة

إذا واجهت أي مشاكل:

1. **تحقق من السجلات**: راجع ملفات السجل في مجلد `logs/`
2. **راجع الوثائق**: اقرأ ملف `README.md`
3. **تواصل معنا**: استخدم قنوات الدعم المذكورة في README

## ✅ التحقق من التثبيت الناجح

بعد التثبيت، تأكد من:

- [ ] تشغيل الخادم بدون أخطاء
- [ ] الوصول للصفحة الرئيسية
- [ ] تسجيل الدخول بنجاح
- [ ] عمل لوحة التحكم
- [ ] إمكانية بدء فحص تجريبي

---

**تهانينا! 🎉 تم تثبيت CyberWeb Hunter بنجاح**

الآن يمكنك البدء في استكشاف عالم الأمن السيبراني الأخلاقي!
