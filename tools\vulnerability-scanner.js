// Vulnerability Scanner Tools for CyberWeb Hunter

const axios = require('axios');
const cheerio = require('cheerio');
const { URL } = require('url');

class VulnerabilityScanner {
    constructor(options = {}) {
        this.options = {
            timeout: 10000,
            userAgent: 'CyberWeb-Hunter/1.0 (Security Scanner)',
            maxRedirects: 5,
            ...options
        };
        
        this.vulnerabilities = [];
    }

    // SQL Injection Scanner
    async scanSQLInjection(targetUrl) {
        console.log(`🔍 Starting SQL Injection scan for: ${targetUrl}`);
        
        const sqlPayloads = [
            "' OR '1'='1",
            "' OR 1=1--",
            "' UNION SELECT NULL--",
            "'; DROP TABLE users--",
            "' OR 'x'='x",
            "1' OR '1'='1' /*",
            "' OR 1=1#",
            "admin'--",
            "admin' #",
            "admin'/*",
            "' or 1=1 limit 1 -- -+",
            "' or '1'='1' limit 1 -- -+"
        ];

        const errorPatterns = [
            /mysql_fetch_array/i,
            /ORA-\d{5}/i,
            /Microsoft.*ODBC.*SQL Server/i,
            /PostgreSQL.*ERROR/i,
            /Warning.*mysql_/i,
            /valid MySQL result/i,
            /MySqlClient\./i,
            /SQL syntax.*MySQL/i,
            /Warning.*\Wmysql_/i,
            /valid MySQL result/i,
            /PostgreSQL query failed/i,
            /Warning.*\Wpg_/i,
            /valid PostgreSQL result/i,
            /Warning.*\Wifilesystem/i,
            /Warning.*\Wfile/i,
            /Warning.*\Wfopen/i,
            /Warning.*\Wfread/i,
            /Warning.*\Wfile_get_contents/i
        ];

        try {
            const baseUrl = new URL(targetUrl);
            const testUrls = this.generateTestUrls(baseUrl);

            for (const testUrl of testUrls) {
                for (const payload of sqlPayloads) {
                    try {
                        const response = await this.makeRequest(testUrl + encodeURIComponent(payload));
                        
                        // Check for SQL error patterns
                        for (const pattern of errorPatterns) {
                            if (pattern.test(response.data)) {
                                this.vulnerabilities.push({
                                    type: 'SQL Injection',
                                    severity: 'High',
                                    url: testUrl,
                                    payload: payload,
                                    description: 'Potential SQL injection vulnerability detected through error-based testing',
                                    evidence: `Error pattern found: ${pattern.source}`,
                                    recommendation: 'Use parameterized queries and input validation'
                                });
                                break;
                            }
                        }

                        // Time-based detection (basic)
                        const startTime = Date.now();
                        await this.makeRequest(testUrl + encodeURIComponent("' OR SLEEP(5)--"));
                        const endTime = Date.now();
                        
                        if (endTime - startTime > 4000) {
                            this.vulnerabilities.push({
                                type: 'SQL Injection (Time-based)',
                                severity: 'High',
                                url: testUrl,
                                payload: "' OR SLEEP(5)--",
                                description: 'Time-based SQL injection vulnerability detected',
                                evidence: `Response delayed by ${endTime - startTime}ms`,
                                recommendation: 'Use parameterized queries and input validation'
                            });
                        }

                    } catch (error) {
                        // Some errors might indicate vulnerabilities
                        if (error.response && error.response.status === 500) {
                            this.vulnerabilities.push({
                                type: 'SQL Injection (Error-based)',
                                severity: 'Medium',
                                url: testUrl,
                                payload: payload,
                                description: 'Server error triggered by SQL injection payload',
                                evidence: 'HTTP 500 Internal Server Error',
                                recommendation: 'Implement proper error handling and input validation'
                            });
                        }
                    }
                }
            }

        } catch (error) {
            console.error('SQL Injection scan error:', error.message);
        }

        console.log(`✅ SQL Injection scan completed. Found ${this.vulnerabilities.filter(v => v.type.includes('SQL')).length} potential vulnerabilities.`);
    }

    // XSS Scanner
    async scanXSS(targetUrl) {
        console.log(`🔍 Starting XSS scan for: ${targetUrl}`);

        const xssPayloads = [
            '<script>alert("XSS")</script>',
            '<img src=x onerror=alert("XSS")>',
            '<svg onload=alert("XSS")>',
            '"><script>alert("XSS")</script>',
            "'><script>alert('XSS')</script>",
            '<iframe src="javascript:alert(\'XSS\')">',
            '<body onload=alert("XSS")>',
            '<input onfocus=alert("XSS") autofocus>',
            '<select onfocus=alert("XSS") autofocus>',
            '<textarea onfocus=alert("XSS") autofocus>',
            '<keygen onfocus=alert("XSS") autofocus>',
            '<video><source onerror="alert(\'XSS\')">',
            '<audio src=x onerror=alert("XSS")>',
            '<details open ontoggle=alert("XSS")>',
            'javascript:alert("XSS")',
            '<script>confirm("XSS")</script>',
            '<script>prompt("XSS")</script>'
        ];

        try {
            const baseUrl = new URL(targetUrl);
            const testUrls = this.generateTestUrls(baseUrl);

            for (const testUrl of testUrls) {
                for (const payload of xssPayloads) {
                    try {
                        const response = await this.makeRequest(testUrl + encodeURIComponent(payload));
                        
                        // Check if payload is reflected in response
                        if (response.data.includes(payload) || 
                            response.data.includes(payload.replace(/"/g, '&quot;')) ||
                            response.data.includes(payload.replace(/'/g, '&#x27;'))) {
                            
                            this.vulnerabilities.push({
                                type: 'Cross-Site Scripting (XSS)',
                                severity: 'Medium',
                                url: testUrl,
                                payload: payload,
                                description: 'Reflected XSS vulnerability detected',
                                evidence: 'Payload reflected in response without proper encoding',
                                recommendation: 'Implement proper input sanitization and output encoding'
                            });
                        }

                        // Check for DOM-based XSS patterns
                        const $ = cheerio.load(response.data);
                        const dangerousPatterns = [
                            'document.write',
                            'innerHTML',
                            'outerHTML',
                            'document.location',
                            'window.location'
                        ];

                        $('script').each((i, elem) => {
                            const scriptContent = $(elem).html();
                            for (const pattern of dangerousPatterns) {
                                if (scriptContent && scriptContent.includes(pattern)) {
                                    this.vulnerabilities.push({
                                        type: 'DOM-based XSS (Potential)',
                                        severity: 'Low',
                                        url: testUrl,
                                        payload: 'N/A',
                                        description: `Potentially dangerous JavaScript pattern found: ${pattern}`,
                                        evidence: `Script contains: ${pattern}`,
                                        recommendation: 'Review JavaScript code for DOM-based XSS vulnerabilities'
                                    });
                                }
                            }
                        });

                    } catch (error) {
                        // Continue with next payload
                    }
                }
            }

        } catch (error) {
            console.error('XSS scan error:', error.message);
        }

        console.log(`✅ XSS scan completed. Found ${this.vulnerabilities.filter(v => v.type.includes('XSS')).length} potential vulnerabilities.`);
    }

    // CSRF Scanner
    async scanCSRF(targetUrl) {
        console.log(`🔍 Starting CSRF scan for: ${targetUrl}`);

        try {
            const response = await this.makeRequest(targetUrl);
            const $ = cheerio.load(response.data);

            // Check for forms without CSRF protection
            $('form').each((i, form) => {
                const $form = $(form);
                const method = $form.attr('method')?.toLowerCase() || 'get';
                
                if (method === 'post') {
                    const hasCSRFToken = $form.find('input[name*="csrf"], input[name*="token"], input[name*="_token"]').length > 0;
                    
                    if (!hasCSRFToken) {
                        this.vulnerabilities.push({
                            type: 'Cross-Site Request Forgery (CSRF)',
                            severity: 'Medium',
                            url: targetUrl,
                            payload: 'N/A',
                            description: 'Form without CSRF protection detected',
                            evidence: `Form action: ${$form.attr('action') || 'current page'}`,
                            recommendation: 'Implement CSRF tokens for all state-changing operations'
                        });
                    }
                }
            });

        } catch (error) {
            console.error('CSRF scan error:', error.message);
        }

        console.log(`✅ CSRF scan completed. Found ${this.vulnerabilities.filter(v => v.type.includes('CSRF')).length} potential vulnerabilities.`);
    }

    // Directory Traversal Scanner
    async scanDirectoryTraversal(targetUrl) {
        console.log(`🔍 Starting Directory Traversal scan for: ${targetUrl}`);

        const traversalPayloads = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\drivers\\etc\\hosts',
            '../../../windows/win.ini',
            '../../../../etc/shadow',
            '../../../proc/version',
            '..\\..\\..\\boot.ini',
            '../../../etc/hosts',
            '../../../../windows/system32/config/sam'
        ];

        const linuxPatterns = [
            /root:.*:0:0:/,
            /daemon:.*:1:1:/,
            /bin:.*:2:2:/,
            /Linux version/i
        ];

        const windowsPatterns = [
            /\[fonts\]/i,
            /\[extensions\]/i,
            /\[mci extensions\]/i,
            /\[boot loader\]/i
        ];

        try {
            const baseUrl = new URL(targetUrl);
            const testUrls = this.generateTestUrls(baseUrl);

            for (const testUrl of testUrls) {
                for (const payload of traversalPayloads) {
                    try {
                        const response = await this.makeRequest(testUrl + encodeURIComponent(payload));
                        
                        // Check for Linux file patterns
                        for (const pattern of linuxPatterns) {
                            if (pattern.test(response.data)) {
                                this.vulnerabilities.push({
                                    type: 'Directory Traversal',
                                    severity: 'High',
                                    url: testUrl,
                                    payload: payload,
                                    description: 'Directory traversal vulnerability detected (Linux)',
                                    evidence: `System file content detected: ${pattern.source}`,
                                    recommendation: 'Implement proper input validation and file access controls'
                                });
                                break;
                            }
                        }

                        // Check for Windows file patterns
                        for (const pattern of windowsPatterns) {
                            if (pattern.test(response.data)) {
                                this.vulnerabilities.push({
                                    type: 'Directory Traversal',
                                    severity: 'High',
                                    url: testUrl,
                                    payload: payload,
                                    description: 'Directory traversal vulnerability detected (Windows)',
                                    evidence: `System file content detected: ${pattern.source}`,
                                    recommendation: 'Implement proper input validation and file access controls'
                                });
                                break;
                            }
                        }

                    } catch (error) {
                        // Continue with next payload
                    }
                }
            }

        } catch (error) {
            console.error('Directory Traversal scan error:', error.message);
        }

        console.log(`✅ Directory Traversal scan completed. Found ${this.vulnerabilities.filter(v => v.type.includes('Directory')).length} potential vulnerabilities.`);
    }

    // Generate test URLs for parameter-based testing
    generateTestUrls(baseUrl) {
        const testUrls = [
            `${baseUrl.origin}${baseUrl.pathname}?id=`,
            `${baseUrl.origin}${baseUrl.pathname}?page=`,
            `${baseUrl.origin}${baseUrl.pathname}?user=`,
            `${baseUrl.origin}${baseUrl.pathname}?search=`,
            `${baseUrl.origin}${baseUrl.pathname}?q=`,
            `${baseUrl.origin}${baseUrl.pathname}?file=`,
            `${baseUrl.origin}${baseUrl.pathname}?path=`,
            `${baseUrl.origin}${baseUrl.pathname}?url=`,
            `${baseUrl.origin}${baseUrl.pathname}?redirect=`,
            `${baseUrl.origin}${baseUrl.pathname}?category=`
        ];

        // Add existing query parameters
        if (baseUrl.search) {
            const params = new URLSearchParams(baseUrl.search);
            for (const [key, value] of params) {
                testUrls.push(`${baseUrl.origin}${baseUrl.pathname}?${key}=`);
            }
        }

        return testUrls;
    }

    // Make HTTP request with proper configuration
    async makeRequest(url, options = {}) {
        const config = {
            method: 'GET',
            url: url,
            timeout: this.options.timeout,
            maxRedirects: this.options.maxRedirects,
            headers: {
                'User-Agent': this.options.userAgent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            },
            validateStatus: () => true, // Don't throw on HTTP errors
            ...options
        };

        return await axios(config);
    }

    // Get all discovered vulnerabilities
    getVulnerabilities() {
        return this.vulnerabilities;
    }

    // Clear vulnerabilities
    clearVulnerabilities() {
        this.vulnerabilities = [];
    }

    // Run comprehensive scan
    async runComprehensiveScan(targetUrl, scanTypes = ['sql_injection', 'xss', 'csrf', 'directory_traversal']) {
        console.log(`🚀 Starting comprehensive scan for: ${targetUrl}`);
        console.log(`📋 Scan types: ${scanTypes.join(', ')}`);

        this.clearVulnerabilities();

        for (const scanType of scanTypes) {
            switch (scanType) {
                case 'sql_injection':
                    await this.scanSQLInjection(targetUrl);
                    break;
                case 'xss':
                    await this.scanXSS(targetUrl);
                    break;
                case 'csrf':
                    await this.scanCSRF(targetUrl);
                    break;
                case 'directory_traversal':
                    await this.scanDirectoryTraversal(targetUrl);
                    break;
                default:
                    console.log(`⚠️  Unknown scan type: ${scanType}`);
            }
        }

        console.log(`🎉 Comprehensive scan completed! Found ${this.vulnerabilities.length} total vulnerabilities.`);
        return this.vulnerabilities;
    }
}

module.exports = VulnerabilityScanner;
