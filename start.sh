#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Clear screen and show banner
clear
echo -e "${CYAN}"
echo "  ========================================"
echo "  🛡️  CyberWeb Hunter v1.0"
echo "  ========================================"
echo "  أداة اختبار الاختراق الأخلاقي"
echo "  Ethical Penetration Testing Tool"
echo "  ========================================"
echo -e "${NC}"

# Check if Node.js is installed
print_info "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed!"
    print_info "Please install Node.js from: https://nodejs.org"
    print_info "Or use your package manager:"
    echo "  Ubuntu/Debian: sudo apt install nodejs npm"
    echo "  CentOS/RHEL:   sudo yum install nodejs npm"
    echo "  macOS:         brew install node"
    exit 1
fi

print_success "Node.js is installed"
node --version

# Check if npm is available
print_info "Checking npm installation..."
if ! command -v npm &> /dev/null; then
    print_error "npm is not available!"
    print_info "Please reinstall Node.js with npm included."
    exit 1
fi

print_success "npm is available"
npm --version

# Check Node.js version
NODE_VERSION=$(node --version | cut -d'v' -f2)
REQUIRED_VERSION="16.0.0"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    print_warning "Node.js version $NODE_VERSION detected. Recommended: $REQUIRED_VERSION or higher"
fi

echo

# Install dependencies if needed
print_info "Checking if dependencies are installed..."
if [ ! -d "node_modules" ]; then
    print_info "Installing dependencies... This may take a few minutes."
    print_info "Please wait..."
    
    if npm install; then
        print_success "Dependencies installed successfully!"
    else
        print_error "Failed to install dependencies!"
        print_info "Please check your internet connection and try again."
        exit 1
    fi
else
    print_success "Dependencies already installed"
fi

echo

# Create required directories
print_info "Creating required directories..."
mkdir -p database reports uploads logs

# Set permissions
chmod 755 database reports uploads logs

echo

# Show startup information
print_info "Starting CyberWeb Hunter server..."
print_info "Please wait while the server initializes..."

echo
echo -e "${CYAN}========================================"
echo "  🚀 Server Starting..."
echo "========================================${NC}"
echo
print_info "Access URLs:"
echo "  📱 Main Page: http://localhost:3000"
echo "  📊 Dashboard: http://localhost:3000/dashboard"
echo "  ⚙️  Admin Panel: http://localhost:3000/admin"
echo
print_info "Default Admin Credentials:"
echo "  👤 Username: admin"
echo "  🔑 Password: JaMaL@123"
echo
echo -e "${YELLOW}========================================"
echo "  ⚠️  LEGAL WARNING"
echo "========================================"
echo "  This tool is for EDUCATIONAL and ETHICAL"
echo "  purposes ONLY. Always get permission"
echo "  before testing any website."
echo "========================================${NC}"
echo
print_info "Press Ctrl+C to stop the server"
print_info "Browser will open automatically in 2 seconds..."
echo

# Function to handle cleanup on exit
cleanup() {
    echo
    print_info "Server stopped."
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Start the server
npm start

# If we reach here, the server has stopped
cleanup
