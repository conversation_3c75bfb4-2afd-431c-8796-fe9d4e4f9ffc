/* Dashboard Styles for CyberWeb Hunter */

/* Navigation */
.navbar {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-brand i {
    font-size: 2rem;
    text-shadow: 0 0 10px var(--primary-color);
}

.nav-menu {
    display: flex;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 10px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(0, 255, 65, 0.1);
    transform: translateY(-2px);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 50%;
    transform: translateX(-50%);
    width: 4px;
    height: 4px;
    background: var(--primary-color);
    border-radius: 50%;
    box-shadow: 0 0 10px var(--primary-color);
}

.nav-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-primary);
}

.user-info i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.logout-btn {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.logout-btn:hover {
    color: var(--danger-color);
    border-color: var(--danger-color);
    background: rgba(255, 0, 64, 0.1);
}

/* Main Content */
.main-content {
    margin-top: 80px;
    padding: 2rem;
    max-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
    text-align: center;
}

.section-header h1 {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.section-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 255, 65, 0.2);
    border-color: var(--primary-color);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--dark-bg);
}

.stat-content h3 {
    font-size: 2rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.dashboard-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.dashboard-card h3 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.dashboard-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.quick-scan-form {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.quick-scan-form input {
    flex: 1;
    min-width: 200px;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
}

.quick-scan-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    color: var(--dark-bg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 255, 65, 0.4);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #6f42c1);
    color: white;
}

.btn-danger {
    background: linear-gradient(45deg, var(--danger-color), #dc3545);
    color: white;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Scanner Container */
.scanner-container {
    max-width: 800px;
    margin: 0 auto;
}

.scan-config {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.scan-config h3 {
    color: var(--primary-color);
    margin-bottom: 2rem;
    font-size: 1.5rem;
}

.form-group {
    margin-bottom: 2rem;
}

.form-group label {
    display: block;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-group input[type="url"] {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
}

.form-group input[type="url"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

.scan-types,
.advanced-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.checkbox-label:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-label .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    margin-left: 0.75rem;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--dark-bg);
    font-weight: bold;
    font-size: 14px;
}

/* Progress and Results */
.scan-progress,
.scan-results {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
}

.scan-progress h3 {
    color: var(--primary-color);
    margin-bottom: 2rem;
}

.progress-bar {
    width: 100%;
    height: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    width: 0%;
    transition: width 0.3s ease;
}

.scan-status {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.results-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

/* Recent Results */
.recent-results {
    max-height: 300px;
    overflow-y: auto;
}

.no-data {
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
    font-style: italic;
}

/* History and Reports */
.history-container,
.reports-container {
    max-width: 1000px;
    margin: 0 auto;
}

.history-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.history-filters input,
.history-filters select {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 1rem;
}

.history-filters input {
    flex: 1;
    min-width: 200px;
}

.history-list,
.reports-list {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
}

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.tool-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 255, 65, 0.2);
    border-color: var(--primary-color);
}

.tool-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.tool-card h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.tool-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

/* Loading */
.loading {
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 1rem;
    }

    .nav-menu {
        display: none;
    }

    .main-content {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .quick-scan-form {
        flex-direction: column;
    }

    .scan-types,
    .advanced-options {
        grid-template-columns: 1fr;
    }

    .results-actions {
        flex-direction: column;
    }

    .history-filters {
        flex-direction: column;
    }

    .tools-grid {
        grid-template-columns: 1fr;
    }
}
