version: '3.8'

services:
  cyberweb-hunter:
    build: .
    container_name: cyberweb-hunter
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
    volumes:
      - ./database:/app/database
      - ./reports:/app/reports
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    restart: unless-stopped
    networks:
      - cyberweb-network
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy
  nginx:
    image: nginx:alpine
    container_name: cyberweb-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - cyberweb-hunter
    restart: unless-stopped
    networks:
      - cyberweb-network
    profiles:
      - production

networks:
  cyberweb-network:
    driver: bridge

volumes:
  cyberweb-data:
    driver: local
