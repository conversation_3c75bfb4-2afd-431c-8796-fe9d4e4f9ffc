/* CyberWeb Hunter - Legendary Cyber Security Tool Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00ff41;
    --secondary-color: #0d7377;
    --accent-color: #14a085;
    --danger-color: #ff0040;
    --warning-color: #ffaa00;
    --success-color: #00ff41;
    --dark-bg: #0a0a0a;
    --darker-bg: #050505;
    --card-bg: rgba(13, 115, 119, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.05);
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: rgba(0, 255, 65, 0.3);
    --shadow-color: rgba(0, 255, 65, 0.2);
    --font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    font-family: var(--font-family);
    background: var(--dark-bg);
    color: var(--text-primary);
    overflow-x: hidden;
    min-height: 100vh;
    position: relative;
}

/* Matrix Background */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.3;
}

#matrix-canvas {
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--dark-bg), var(--darker-bg));
}

/* Container */
.container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
}

/* Header */
.header {
    text-align: center;
    padding: 2rem 1rem;
    background: linear-gradient(135deg, rgba(0, 255, 65, 0.1), rgba(13, 115, 119, 0.1));
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.logo-icon {
    font-size: 3rem;
    color: var(--primary-color);
    text-shadow: 0 0 20px var(--primary-color);
    animation: pulse 2s infinite;
}

.logo-text {
    font-size: 2.5rem;
    font-weight: 900;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px var(--primary-color);
}

.version {
    background: var(--primary-color);
    color: var(--dark-bg);
    padding: 0.2rem 0.5rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.tagline {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-top: 0.5rem;
}

/* Login Container */
.login-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 3rem;
    flex-wrap: wrap;
}

/* Login Box */
.login-box {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    box-shadow: 
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 50px var(--shadow-color);
    position: relative;
    overflow: hidden;
}

.login-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    animation: scan 3s infinite;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-header i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    text-shadow: 0 0 20px var(--primary-color);
}

.login-header h2 {
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.login-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Input Groups */
.input-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.input-group i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    z-index: 2;
}

.input-group input {
    width: 100%;
    padding: 15px 50px 15px 15px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 255, 65, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.input-group input::placeholder {
    color: var(--text-secondary);
}

.toggle-password {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: var(--primary-color);
}

/* Login Options */
.login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.remember-me input {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
    margin-left: 8px;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.remember-me input:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--dark-bg);
    font-weight: bold;
    font-size: 12px;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, var(--primary-color), var(--accent-color));
    border: none;
    border-radius: 10px;
    color: var(--dark-bg);
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 255, 65, 0.4);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.login-btn:hover::before {
    left: 100%;
}

/* Demo Credentials */
.demo-credentials {
    margin-top: 2rem;
    padding: 1rem;
    background: rgba(255, 170, 0, 0.1);
    border: 1px solid rgba(255, 170, 0, 0.3);
    border-radius: 10px;
    text-align: center;
}

.demo-credentials h4 {
    color: var(--warning-color);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.demo-credentials .credential-item {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin: 0.3rem 0;
}

.demo-credentials strong {
    color: var(--primary-color);
}

/* Features Preview */
.features-preview {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.features-preview h3 {
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.feature-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 255, 65, 0.2);
    border-color: var(--primary-color);
}

.feature-item i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.feature-item span {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
}

/* Footer */
.footer {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-top: 1px solid var(--border-color);
    padding: 1.5rem;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-content p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: var(--text-secondary);
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    color: var(--text-primary);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

/* Alert System */
.alert-system {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
}

.alert {
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid;
    animation: slideIn 0.3s ease;
    max-width: 400px;
}

.alert.success {
    background: rgba(0, 255, 65, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.alert.error {
    background: rgba(255, 0, 64, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.alert.warning {
    background: rgba(255, 170, 0, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

/* Animations */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-container {
        flex-direction: column;
        gap: 2rem;
    }

    .login-box,
    .features-preview {
        max-width: 100%;
    }

    .logo-text {
        font-size: 2rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 1rem;
    }

    .login-box {
        padding: 2rem 1.5rem;
    }

    .logo-container {
        flex-direction: column;
        gap: 0.5rem;
    }

    .logo-text {
        font-size: 1.5rem;
    }
}
