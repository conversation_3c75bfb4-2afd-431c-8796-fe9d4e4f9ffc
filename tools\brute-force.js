// Brute Force Scanner for CyberWeb Hunter

const axios = require('axios');
const { URL } = require('url');

class BruteForceScanner {
    constructor(options = {}) {
        this.options = {
            timeout: 5000,
            maxConcurrent: 5,
            delay: 100,
            userAgent: 'CyberWeb-Hunter/1.0 (Security Scanner)',
            ...options
        };
        
        this.vulnerabilities = [];
        this.commonPasswords = [
            'admin', 'password', '123456', 'admin123', 'root', 'toor',
            'pass', 'test', 'guest', 'user', 'demo', 'qwerty',
            '12345', 'password123', 'admin1', 'administrator',
            'letmein', 'welcome', 'monkey', 'dragon', 'master',
            'shadow', 'login', 'access', 'secret', 'system'
        ];
        
        this.commonUsernames = [
            'admin', 'administrator', 'root', 'user', 'test',
            'guest', 'demo', 'operator', 'manager', 'support',
            'service', 'oracle', 'postgres', 'mysql', 'sa',
            'webmaster', 'www', 'ftp', 'mail', 'email'
        ];

        this.commonDirectories = [
            'admin', 'administrator', 'wp-admin', 'phpmyadmin',
            'cpanel', 'control', 'panel', 'login', 'signin',
            'dashboard', 'manager', 'management', 'console',
            'backend', 'backoffice', 'private', 'secure',
            'restricted', 'internal', 'staff', 'employee'
        ];

        this.commonFiles = [
            'robots.txt', 'sitemap.xml', '.htaccess', 'web.config',
            'config.php', 'configuration.php', 'settings.php',
            'database.php', 'db.php', 'connect.php', 'connection.php',
            'backup.sql', 'dump.sql', 'database.sql', 'db.sql',
            '.env', '.git/config', '.svn/entries', 'composer.json',
            'package.json', 'readme.txt', 'changelog.txt',
            'install.php', 'setup.php', 'test.php', 'info.php'
        ];
    }

    // Main brute force scanning function
    async scanBruteForce(targetUrl, scanTypes = ['login', 'directories', 'files']) {
        console.log(`🔨 Starting Brute Force scan for: ${targetUrl}`);
        console.log(`📋 Scan types: ${scanTypes.join(', ')}`);

        this.clearVulnerabilities();

        for (const scanType of scanTypes) {
            switch (scanType) {
                case 'login':
                    await this.scanLoginForms(targetUrl);
                    break;
                case 'directories':
                    await this.scanDirectories(targetUrl);
                    break;
                case 'files':
                    await this.scanFiles(targetUrl);
                    break;
                case 'admin_panels':
                    await this.scanAdminPanels(targetUrl);
                    break;
                default:
                    console.log(`⚠️  Unknown scan type: ${scanType}`);
            }
        }

        console.log(`✅ Brute Force scan completed. Found ${this.vulnerabilities.length} potential issues.`);
        return this.vulnerabilities;
    }

    // Scan for login forms and attempt brute force
    async scanLoginForms(targetUrl) {
        console.log(`🔍 Scanning for login forms...`);

        try {
            // First, find login forms
            const response = await this.makeRequest(targetUrl);
            const loginForms = this.findLoginForms(response.data, targetUrl);

            for (const form of loginForms) {
                await this.bruteForceLogin(form);
            }

            // Also check common login endpoints
            const commonLoginPaths = [
                '/login', '/signin', '/admin/login', '/wp-login.php',
                '/administrator', '/admin', '/cpanel', '/webmail',
                '/phpmyadmin', '/mysql', '/database', '/db'
            ];

            for (const path of commonLoginPaths) {
                try {
                    const loginUrl = new URL(path, targetUrl).href;
                    const response = await this.makeRequest(loginUrl);
                    
                    if (response.status === 200) {
                        const forms = this.findLoginForms(response.data, loginUrl);
                        for (const form of forms) {
                            await this.bruteForceLogin(form);
                        }
                    }
                } catch (error) {
                    // Continue with next path
                }
            }

        } catch (error) {
            console.error('Login form scan error:', error.message);
        }
    }

    // Find login forms in HTML
    findLoginForms(html, baseUrl) {
        const forms = [];
        const formRegex = /<form[^>]*>([\s\S]*?)<\/form>/gi;
        let match;

        while ((match = formRegex.exec(html)) !== null) {
            const formHtml = match[0];
            
            // Check if form contains password field
            if (formHtml.includes('type="password"') || 
                formHtml.includes("type='password'")) {
                
                // Extract form action
                const actionMatch = formHtml.match(/action\s*=\s*["']([^"']*)["']/i);
                const action = actionMatch ? actionMatch[1] : '';
                
                // Extract method
                const methodMatch = formHtml.match(/method\s*=\s*["']([^"']*)["']/i);
                const method = methodMatch ? methodMatch[1].toLowerCase() : 'get';

                // Extract field names
                const usernameFields = this.extractFieldNames(formHtml, ['text', 'email']);
                const passwordFields = this.extractFieldNames(formHtml, ['password']);

                if (usernameFields.length > 0 && passwordFields.length > 0) {
                    forms.push({
                        url: new URL(action || '', baseUrl).href,
                        method: method,
                        usernameField: usernameFields[0],
                        passwordField: passwordFields[0]
                    });
                }
            }
        }

        return forms;
    }

    // Extract field names from form HTML
    extractFieldNames(formHtml, types) {
        const fields = [];
        
        for (const type of types) {
            const regex = new RegExp(`<input[^>]*type\\s*=\\s*["']${type}["'][^>]*name\\s*=\\s*["']([^"']*)["']`, 'gi');
            let match;
            
            while ((match = regex.exec(formHtml)) !== null) {
                fields.push(match[1]);
            }
        }

        return fields;
    }

    // Attempt brute force on login form
    async bruteForceLogin(form) {
        console.log(`🔓 Attempting brute force on: ${form.url}`);

        let attempts = 0;
        const maxAttempts = 25; // Limit attempts to avoid detection

        for (const username of this.commonUsernames) {
            for (const password of this.commonPasswords) {
                if (attempts >= maxAttempts) break;

                try {
                    const success = await this.attemptLogin(form, username, password);
                    
                    if (success) {
                        this.vulnerabilities.push({
                            type: 'Weak Authentication',
                            severity: 'High',
                            url: form.url,
                            payload: `${username}:${password}`,
                            description: 'Weak credentials discovered through brute force',
                            evidence: `Successful login with ${username}:${password}`,
                            recommendation: 'Implement strong password policies and account lockout mechanisms'
                        });
                        
                        console.log(`✅ Found weak credentials: ${username}:${password}`);
                        return; // Stop after finding one
                    }

                    attempts++;
                    await this.delay(this.options.delay);

                } catch (error) {
                    // Continue with next combination
                }
            }
            
            if (attempts >= maxAttempts) break;
        }
    }

    // Attempt single login
    async attemptLogin(form, username, password) {
        try {
            const data = {
                [form.usernameField]: username,
                [form.passwordField]: password
            };

            const response = await this.makeRequest(form.url, {
                method: form.method.toUpperCase(),
                data: form.method === 'post' ? data : undefined,
                params: form.method === 'get' ? data : undefined
            });

            // Check for successful login indicators
            const successIndicators = [
                'dashboard', 'welcome', 'logout', 'profile',
                'admin panel', 'control panel', 'management',
                'successfully logged in', 'login successful'
            ];

            const failureIndicators = [
                'invalid', 'incorrect', 'wrong', 'failed',
                'error', 'denied', 'unauthorized', 'forbidden'
            ];

            const responseText = response.data.toLowerCase();

            // Check for failure indicators first
            for (const indicator of failureIndicators) {
                if (responseText.includes(indicator)) {
                    return false;
                }
            }

            // Check for success indicators
            for (const indicator of successIndicators) {
                if (responseText.includes(indicator)) {
                    return true;
                }
            }

            // Check for redirect (common after successful login)
            if (response.status === 302 || response.status === 301) {
                return true;
            }

            return false;

        } catch (error) {
            return false;
        }
    }

    // Scan for common directories
    async scanDirectories(targetUrl) {
        console.log(`📁 Scanning for common directories...`);

        const baseUrl = new URL(targetUrl);
        
        for (const directory of this.commonDirectories) {
            try {
                const dirUrl = new URL(`/${directory}/`, baseUrl).href;
                const response = await this.makeRequest(dirUrl);

                if (response.status === 200) {
                    this.vulnerabilities.push({
                        type: 'Directory Disclosure',
                        severity: 'Low',
                        url: dirUrl,
                        payload: 'N/A',
                        description: `Accessible directory found: /${directory}/`,
                        evidence: `HTTP ${response.status} response`,
                        recommendation: 'Restrict access to sensitive directories'
                    });
                    
                    console.log(`📂 Found directory: /${directory}/`);
                }

                await this.delay(this.options.delay);

            } catch (error) {
                // Continue with next directory
            }
        }
    }

    // Scan for common files
    async scanFiles(targetUrl) {
        console.log(`📄 Scanning for common files...`);

        const baseUrl = new URL(targetUrl);
        
        for (const file of this.commonFiles) {
            try {
                const fileUrl = new URL(`/${file}`, baseUrl).href;
                const response = await this.makeRequest(fileUrl);

                if (response.status === 200) {
                    let severity = 'Low';
                    
                    // Determine severity based on file type
                    if (file.includes('config') || file.includes('database') || 
                        file.includes('.env') || file.includes('backup')) {
                        severity = 'High';
                    } else if (file.includes('.git') || file.includes('.svn') || 
                               file.includes('admin') || file.includes('install')) {
                        severity = 'Medium';
                    }

                    this.vulnerabilities.push({
                        type: 'Information Disclosure',
                        severity: severity,
                        url: fileUrl,
                        payload: 'N/A',
                        description: `Sensitive file accessible: ${file}`,
                        evidence: `HTTP ${response.status} response, Content-Length: ${response.headers['content-length'] || 'unknown'}`,
                        recommendation: 'Remove or restrict access to sensitive files'
                    });
                    
                    console.log(`📋 Found file: ${file}`);
                }

                await this.delay(this.options.delay);

            } catch (error) {
                // Continue with next file
            }
        }
    }

    // Scan for admin panels
    async scanAdminPanels(targetUrl) {
        console.log(`⚙️  Scanning for admin panels...`);

        const adminPaths = [
            '/admin', '/administrator', '/admin.php', '/admin.html',
            '/wp-admin', '/wp-admin/', '/cpanel', '/cpanel/',
            '/phpmyadmin', '/phpmyadmin/', '/adminer.php',
            '/admin/login', '/admin/login.php', '/admin/index.php',
            '/backend', '/backend/', '/control', '/control/',
            '/panel', '/panel/', '/manage', '/manage/',
            '/dashboard', '/dashboard/', '/console', '/console/'
        ];

        const baseUrl = new URL(targetUrl);
        
        for (const path of adminPaths) {
            try {
                const adminUrl = new URL(path, baseUrl).href;
                const response = await this.makeRequest(adminUrl);

                if (response.status === 200) {
                    // Check if it looks like an admin panel
                    const adminIndicators = [
                        'admin', 'administrator', 'login', 'dashboard',
                        'control panel', 'management', 'backend'
                    ];

                    const responseText = response.data.toLowerCase();
                    const isAdminPanel = adminIndicators.some(indicator => 
                        responseText.includes(indicator)
                    );

                    if (isAdminPanel) {
                        this.vulnerabilities.push({
                            type: 'Admin Panel Exposure',
                            severity: 'Medium',
                            url: adminUrl,
                            payload: 'N/A',
                            description: `Admin panel accessible: ${path}`,
                            evidence: `HTTP ${response.status} response`,
                            recommendation: 'Restrict access to admin panels using IP whitelisting or VPN'
                        });
                        
                        console.log(`🔧 Found admin panel: ${path}`);
                    }
                }

                await this.delay(this.options.delay);

            } catch (error) {
                // Continue with next path
            }
        }
    }

    // Make HTTP request
    async makeRequest(url, options = {}) {
        const config = {
            method: 'GET',
            url: url,
            timeout: this.options.timeout,
            maxRedirects: 5,
            headers: {
                'User-Agent': this.options.userAgent,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Connection': 'keep-alive'
            },
            validateStatus: () => true, // Don't throw on HTTP errors
            ...options
        };

        return await axios(config);
    }

    // Delay function
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // Get all vulnerabilities
    getVulnerabilities() {
        return this.vulnerabilities;
    }

    // Clear vulnerabilities
    clearVulnerabilities() {
        this.vulnerabilities = [];
    }
}

module.exports = BruteForceScanner;
