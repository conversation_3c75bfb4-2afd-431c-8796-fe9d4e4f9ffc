<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - CyberWeb Hunter</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Matrix Background -->
    <div class="matrix-bg">
        <canvas id="matrix-canvas"></canvas>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-shield-alt"></i>
                <span>CyberWeb Hunter</span>
            </div>
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </a>
                <a href="#scanner" class="nav-link" data-section="scanner">
                    <i class="fas fa-search"></i>
                    <span>الماسح الضوئي</span>
                </a>
                <a href="#history" class="nav-link" data-section="history">
                    <i class="fas fa-history"></i>
                    <span>السجل</span>
                </a>
                <a href="#reports" class="nav-link" data-section="reports">
                    <i class="fas fa-file-pdf"></i>
                    <span>التقارير</span>
                </a>
                <a href="#tools" class="nav-link" data-section="tools">
                    <i class="fas fa-tools"></i>
                    <span>الأدوات</span>
                </a>
            </div>
            <div class="nav-user">
                <div class="user-info">
                    <span id="username">المستخدم</span>
                    <i class="fas fa-user-circle"></i>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="content-section active">
            <div class="section-header">
                <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
                <p>مرحباً بك في CyberWeb Hunter - أداة اختبار الاختراق الأخلاقي</p>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalScans">0</h3>
                        <p>إجمالي عمليات الفحص</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-bug"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalVulns">0</h3>
                        <p>الثغرات المكتشفة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalReports">0</h3>
                        <p>التقارير المُنشأة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="lastScan">لا يوجد</h3>
                        <p>آخر فحص</p>
                    </div>
                </div>
            </div>

            <div class="dashboard-grid">
                <div class="dashboard-card">
                    <h3><i class="fas fa-rocket"></i> بدء فحص سريع</h3>
                    <p>ابدأ فحص أمني سريع لموقع ويب</p>
                    <div class="quick-scan-form">
                        <input type="url" id="quickScanUrl" placeholder="أدخل رابط الموقع">
                        <button onclick="startQuickScan()" class="btn btn-primary">
                            <i class="fas fa-play"></i> ابدأ الفحص
                        </button>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3><i class="fas fa-chart-line"></i> آخر النتائج</h3>
                    <div id="recentResults" class="recent-results">
                        <p class="no-data">لا توجد نتائج حديثة</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Scanner Section -->
        <section id="scanner-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-search"></i> الماسح الضوئي المتقدم</h1>
                <p>قم بإعداد فحص مخصص للموقع المستهدف</p>
            </div>

            <div class="scanner-container">
                <div class="scan-config">
                    <h3><i class="fas fa-cog"></i> إعدادات الفحص</h3>
                    
                    <div class="form-group">
                        <label for="targetUrl">الموقع المستهدف</label>
                        <input type="url" id="targetUrl" placeholder="https://example.com" required>
                    </div>

                    <div class="form-group">
                        <label>أنواع الفحص</label>
                        <div class="scan-types">
                            <label class="checkbox-label">
                                <input type="checkbox" value="sql_injection" checked>
                                <span class="checkmark"></span>
                                SQL Injection
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="xss" checked>
                                <span class="checkmark"></span>
                                Cross-Site Scripting (XSS)
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="csrf">
                                <span class="checkmark"></span>
                                CSRF
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="ssl_check" checked>
                                <span class="checkmark"></span>
                                فحص SSL/TLS
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="directory_traversal">
                                <span class="checkmark"></span>
                                Directory Traversal
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" value="brute_force">
                                <span class="checkmark"></span>
                                Brute Force
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>خيارات متقدمة</label>
                        <div class="advanced-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="useProxy">
                                <span class="checkmark"></span>
                                استخدام البروكسي
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="stealthMode">
                                <span class="checkmark"></span>
                                الوضع الخفي
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="deepScan">
                                <span class="checkmark"></span>
                                فحص عميق
                            </label>
                        </div>
                    </div>

                    <button onclick="startAdvancedScan()" class="btn btn-primary btn-large">
                        <i class="fas fa-play"></i>
                        بدء الفحص المتقدم
                    </button>
                </div>

                <div class="scan-progress" id="scanProgress" style="display: none;">
                    <h3><i class="fas fa-spinner fa-spin"></i> جاري الفحص...</h3>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="scan-status" id="scanStatus">جاري التحضير...</div>
                    <button onclick="stopScan()" class="btn btn-danger">
                        <i class="fas fa-stop"></i>
                        إيقاف الفحص
                    </button>
                </div>

                <div class="scan-results" id="scanResults" style="display: none;">
                    <h3><i class="fas fa-check-circle"></i> نتائج الفحص</h3>
                    <div id="resultsContent"></div>
                    <div class="results-actions">
                        <button onclick="generateReport()" class="btn btn-success">
                            <i class="fas fa-file-pdf"></i>
                            إنشاء تقرير PDF
                        </button>
                        <button onclick="exportResults()" class="btn btn-info">
                            <i class="fas fa-download"></i>
                            تصدير النتائج
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- History Section -->
        <section id="history-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-history"></i> سجل عمليات الفحص</h1>
                <p>عرض جميع عمليات الفحص السابقة ونتائجها</p>
            </div>

            <div class="history-container">
                <div class="history-filters">
                    <input type="text" id="searchHistory" placeholder="البحث في السجل...">
                    <select id="filterStatus">
                        <option value="">جميع الحالات</option>
                        <option value="completed">مكتمل</option>
                        <option value="running">قيد التشغيل</option>
                        <option value="failed">فاشل</option>
                    </select>
                </div>

                <div class="history-list" id="historyList">
                    <div class="loading">جاري تحميل السجل...</div>
                </div>

                <div class="pagination" id="historyPagination"></div>
            </div>
        </section>

        <!-- Reports Section -->
        <section id="reports-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-file-pdf"></i> إدارة التقارير</h1>
                <p>عرض وإدارة جميع التقارير المُنشأة</p>
            </div>

            <div class="reports-container">
                <div class="reports-list" id="reportsList">
                    <div class="loading">جاري تحميل التقارير...</div>
                </div>
            </div>
        </section>

        <!-- Tools Section -->
        <section id="tools-section" class="content-section">
            <div class="section-header">
                <h1><i class="fas fa-tools"></i> الأدوات المساعدة</h1>
                <p>مجموعة من الأدوات المفيدة لاختبار الأمان</p>
            </div>

            <div class="tools-grid">
                <div class="tool-card">
                    <i class="fas fa-globe"></i>
                    <h3>فحص DNS</h3>
                    <p>فحص سجلات DNS للموقع</p>
                    <button class="btn btn-primary">استخدام</button>
                </div>
                <div class="tool-card">
                    <i class="fas fa-network-wired"></i>
                    <h3>فحص المنافذ</h3>
                    <p>فحص المنافذ المفتوحة</p>
                    <button class="btn btn-primary">استخدام</button>
                </div>
                <div class="tool-card">
                    <i class="fas fa-key"></i>
                    <h3>مولد كلمات المرور</h3>
                    <p>إنشاء كلمات مرور قوية</p>
                    <button class="btn btn-primary">استخدام</button>
                </div>
                <div class="tool-card">
                    <i class="fas fa-code"></i>
                    <h3>ترميز/فك الترميز</h3>
                    <p>أدوات الترميز المختلفة</p>
                    <button class="btn btn-primary">استخدام</button>
                </div>
            </div>
        </section>
    </main>

    <!-- Alert System -->
    <div id="alertSystem" class="alert-system"></div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <p>جاري المعالجة...</p>
        </div>
    </div>

    <script src="js/matrix.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
