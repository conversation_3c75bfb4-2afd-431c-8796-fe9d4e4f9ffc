#!/usr/bin/env node

/**
 * CyberWeb Hunter - Connection Test
 * This script tests all major components
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

// Test configuration
const TEST_CONFIG = {
    host: 'localhost',
    port: 3000,
    timeout: 5000
};

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function testFileExists(filePath, description) {
    if (fs.existsSync(filePath)) {
        log(`✅ ${description}`, 'green');
        return true;
    } else {
        log(`❌ ${description} - Missing: ${filePath}`, 'red');
        return false;
    }
}

function testHttpEndpoint(path, description) {
    return new Promise((resolve) => {
        const options = {
            hostname: TEST_CONFIG.host,
            port: TEST_CONFIG.port,
            path: path,
            method: 'GET',
            timeout: TEST_CONFIG.timeout
        };

        const req = http.request(options, (res) => {
            if (res.statusCode === 200 || res.statusCode === 302) {
                log(`✅ ${description} (${res.statusCode})`, 'green');
                resolve(true);
            } else {
                log(`⚠️  ${description} (${res.statusCode})`, 'yellow');
                resolve(false);
            }
        });

        req.on('error', (err) => {
            log(`❌ ${description} - ${err.message}`, 'red');
            resolve(false);
        });

        req.on('timeout', () => {
            log(`❌ ${description} - Timeout`, 'red');
            req.destroy();
            resolve(false);
        });

        req.end();
    });
}

async function runTests() {
    log('🧪 CyberWeb Hunter - System Test', 'cyan');
    log('═'.repeat(50), 'cyan');
    log('');

    // File structure tests
    log('📁 Testing File Structure:', 'blue');
    const fileTests = [
        ['server.js', 'Main server file'],
        ['package.json', 'Package configuration'],
        ['database/init.js', 'Database initialization'],
        ['routes/auth.js', 'Authentication routes'],
        ['routes/scan.js', 'Scanning routes'],
        ['routes/admin.js', 'Admin routes'],
        ['routes/reports.js', 'Reports routes'],
        ['public/index.html', 'Main page'],
        ['public/dashboard.html', 'Dashboard page'],
        ['public/admin.html', 'Admin page'],
        ['public/css/style.css', 'Main stylesheet'],
        ['public/js/main.js', 'Main JavaScript'],
        ['public/js/dashboard.js', 'Dashboard JavaScript'],
        ['public/js/admin.js', 'Admin JavaScript']
    ];

    let fileTestsPassed = 0;
    for (const [file, desc] of fileTests) {
        if (testFileExists(file, desc)) {
            fileTestsPassed++;
        }
    }

    log('');
    log(`📊 File Tests: ${fileTestsPassed}/${fileTests.length} passed`, 
        fileTestsPassed === fileTests.length ? 'green' : 'yellow');
    log('');

    // Directory tests
    log('📂 Testing Directory Structure:', 'blue');
    const directories = ['database', 'reports', 'uploads', 'logs', 'public', 'routes', 'tools'];
    let dirTestsPassed = 0;
    
    for (const dir of directories) {
        if (fs.existsSync(dir)) {
            log(`✅ Directory: ${dir}`, 'green');
            dirTestsPassed++;
        } else {
            log(`❌ Directory missing: ${dir}`, 'red');
        }
    }

    log('');
    log(`📊 Directory Tests: ${dirTestsPassed}/${directories.length} passed`, 
        dirTestsPassed === directories.length ? 'green' : 'yellow');
    log('');

    // HTTP endpoint tests (only if server is running)
    log('🌐 Testing HTTP Endpoints:', 'blue');
    log('   (Server must be running for these tests)', 'yellow');
    
    const endpointTests = [
        ['/', 'Main page'],
        ['/dashboard', 'Dashboard page'],
        ['/admin', 'Admin page'],
        ['/api/health', 'Health check API'],
        ['/api/auth/status', 'Auth status API']
    ];

    let endpointTestsPassed = 0;
    for (const [endpoint, desc] of endpointTests) {
        if (await testHttpEndpoint(endpoint, desc)) {
            endpointTestsPassed++;
        }
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    log('');
    log(`📊 Endpoint Tests: ${endpointTestsPassed}/${endpointTests.length} passed`, 
        endpointTestsPassed === endpointTests.length ? 'green' : 'yellow');
    log('');

    // Summary
    log('📋 Test Summary:', 'cyan');
    log('═'.repeat(30), 'cyan');
    
    const totalTests = fileTests.length + directories.length + endpointTests.length;
    const totalPassed = fileTestsPassed + dirTestsPassed + endpointTestsPassed;
    
    log(`Total Tests: ${totalTests}`, 'blue');
    log(`Passed: ${totalPassed}`, totalPassed === totalTests ? 'green' : 'yellow');
    log(`Failed: ${totalTests - totalPassed}`, totalTests - totalPassed === 0 ? 'green' : 'red');
    
    if (totalPassed === totalTests) {
        log('');
        log('🎉 All tests passed! CyberWeb Hunter is ready to use.', 'green');
        log('');
        log('🚀 To start the application:', 'cyan');
        log('   npm run run', 'yellow');
        log('   or', 'blue');
        log('   node run.js', 'yellow');
    } else {
        log('');
        log('⚠️  Some tests failed. Please check the issues above.', 'yellow');
        
        if (endpointTestsPassed < endpointTests.length) {
            log('');
            log('💡 If endpoint tests failed:', 'blue');
            log('   • Make sure the server is running', 'cyan');
            log('   • Check if port 3000 is available', 'cyan');
            log('   • Run: npm start', 'cyan');
        }
    }
    
    log('');
}

// Run the tests
runTests().catch(error => {
    log('');
    log('💥 Test runner error:', 'red');
    log(`   ${error.message}`, 'yellow');
    process.exit(1);
});
