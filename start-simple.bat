@echo off
title <PERSON>berWeb Hunter - Ethical Penetration Testing Tool

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🛡️  CyberWeb Hunter                      ║
echo ║              Ethical Penetration Testing Tool               ║
echo ║                        Version 1.0.0                        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo [INFO] Starting CyberWeb Hunter...
echo [INFO] Checking Node.js installation...

where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [INFO] Please install Node.js from: https://nodejs.org
    pause
    exit /b 1
)

echo [SUCCESS] Node.js found
echo [INFO] Starting server...
echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │                     Access Information                      │
echo ├─────────────────────────────────────────────────────────────┤
echo │  🏠 Home Page:    http://localhost:3000                    │
echo │  📊 Dashboard:    http://localhost:3000/dashboard          │
echo │  ⚙️  Admin Panel:  http://localhost:3000/admin             │
echo ├─────────────────────────────────────────────────────────────┤
echo │  👤 Username: admin                                        │
echo │  🔑 Password: JaMaL@123                                     │
echo └─────────────────────────────────────────────────────────────┘
echo.
echo [INFO] Browser will open automatically...
echo [INFO] Press Ctrl+C to stop the server
echo.

REM Start the server
node server.js

echo.
echo [INFO] Server stopped
pause
