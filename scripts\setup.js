#!/usr/bin/env node

// Setup script for CyberWeb Hunter

const fs = require('fs-extra');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorLog(message, color = 'reset') {
    console.log(colors[color] + message + colors.reset);
}

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function createDirectories() {
    const directories = [
        'database',
        'reports',
        'uploads',
        'logs',
        'backups',
        'ssl'
    ];

    colorLog('\n📁 Creating required directories...', 'blue');
    
    for (const dir of directories) {
        try {
            await fs.ensureDir(dir);
            colorLog(`✅ Created directory: ${dir}`, 'green');
        } catch (error) {
            colorLog(`❌ Failed to create directory ${dir}: ${error.message}`, 'red');
        }
    }
}

async function createEnvFile() {
    colorLog('\n⚙️  Setting up environment configuration...', 'blue');
    
    if (await fs.pathExists('.env')) {
        const overwrite = await question('📝 .env file already exists. Overwrite? (y/N): ');
        if (overwrite.toLowerCase() !== 'y') {
            colorLog('⏭️  Skipping .env file creation', 'yellow');
            return;
        }
    }

    // Get user preferences
    const port = await question('🌐 Server port (default: 3000): ') || '3000';
    const sessionSecret = await question('🔐 Session secret (leave empty for auto-generated): ') || generateRandomString(32);
    const adminUsername = await question('👤 Admin username (default: admin): ') || 'admin';
    const adminPassword = await question('🔑 Admin password (default: JaMaL@123): ') || 'JaMaL@123';

    const envContent = `# CyberWeb Hunter Environment Configuration
# Generated on ${new Date().toISOString()}

# Server Configuration
PORT=${port}
NODE_ENV=development

# Database Configuration
DB_PATH=./database/cyberweb.db

# Session Configuration
SESSION_SECRET=${sessionSecret}
SESSION_MAX_AGE=86400000

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# Admin Configuration
DEFAULT_ADMIN_USERNAME=${adminUsername}
DEFAULT_ADMIN_PASSWORD=${adminPassword}
DEFAULT_ADMIN_EMAIL=<EMAIL>

# Scanning Configuration
MAX_CONCURRENT_SCANS=10
SCAN_TIMEOUT=300000
REQUEST_TIMEOUT=10000
MAX_REDIRECTS=5

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# File Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads
REPORT_PATH=./reports

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Backup Configuration
BACKUP_PATH=./backups
AUTO_BACKUP_ENABLED=false
BACKUP_INTERVAL=86400000
`;

    try {
        await fs.writeFile('.env', envContent);
        colorLog('✅ Environment file created successfully', 'green');
    } catch (error) {
        colorLog(`❌ Failed to create .env file: ${error.message}`, 'red');
    }
}

async function setupGitIgnore() {
    colorLog('\n📝 Setting up .gitignore...', 'blue');
    
    if (await fs.pathExists('.gitignore')) {
        colorLog('✅ .gitignore already exists', 'green');
        return;
    }

    const gitignoreContent = `# Dependencies
node_modules/
npm-debug.log*

# Environment files
.env
.env.local
.env.production

# Database files
database/*.db
database/*.sqlite*

# Generated files
reports/*.pdf
reports/*.html
uploads/*
!uploads/.gitkeep
logs/*.log
backups/*

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# SSL certificates
ssl/*.key
ssl/*.pem
ssl/*.crt
`;

    try {
        await fs.writeFile('.gitignore', gitignoreContent);
        colorLog('✅ .gitignore created successfully', 'green');
    } catch (error) {
        colorLog(`❌ Failed to create .gitignore: ${error.message}`, 'red');
    }
}

async function checkDependencies() {
    colorLog('\n📦 Checking dependencies...', 'blue');
    
    try {
        const packageJson = await fs.readJson('package.json');
        const dependencies = Object.keys(packageJson.dependencies || {});
        const devDependencies = Object.keys(packageJson.devDependencies || {});
        
        colorLog(`✅ Found ${dependencies.length} dependencies`, 'green');
        colorLog(`✅ Found ${devDependencies.length} dev dependencies`, 'green');
        
        // Check if node_modules exists
        if (await fs.pathExists('node_modules')) {
            colorLog('✅ node_modules directory exists', 'green');
        } else {
            colorLog('⚠️  node_modules not found. Run "npm install" to install dependencies', 'yellow');
        }
        
    } catch (error) {
        colorLog(`❌ Error checking dependencies: ${error.message}`, 'red');
    }
}

async function createStartupScripts() {
    colorLog('\n🚀 Creating startup scripts...', 'blue');
    
    // Windows batch file
    const batchContent = `@echo off
title CyberWeb Hunter
echo Starting CyberWeb Hunter...
npm start
pause`;

    // Unix shell script
    const shellContent = `#!/bin/bash
echo "Starting CyberWeb Hunter..."
npm start`;

    try {
        await fs.writeFile('start.bat', batchContent);
        await fs.writeFile('start.sh', shellContent);
        
        // Make shell script executable on Unix systems
        if (process.platform !== 'win32') {
            await fs.chmod('start.sh', '755');
        }
        
        colorLog('✅ Startup scripts created', 'green');
    } catch (error) {
        colorLog(`❌ Failed to create startup scripts: ${error.message}`, 'red');
    }
}

function generateRandomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

async function showCompletionMessage() {
    colorLog('\n🎉 Setup completed successfully!', 'green');
    colorLog('\n📋 Next steps:', 'cyan');
    colorLog('1. Install dependencies: npm install', 'white');
    colorLog('2. Start the server: npm start', 'white');
    colorLog('3. Open browser: http://localhost:3000', 'white');
    colorLog('4. Login with admin credentials', 'white');
    colorLog('\n⚠️  Remember: Use this tool ethically and legally!', 'yellow');
}

async function main() {
    colorLog('🛡️  CyberWeb Hunter Setup', 'cyan');
    colorLog('=====================================', 'cyan');
    
    try {
        await createDirectories();
        await createEnvFile();
        await setupGitIgnore();
        await checkDependencies();
        await createStartupScripts();
        await showCompletionMessage();
    } catch (error) {
        colorLog(`❌ Setup failed: ${error.message}`, 'red');
        process.exit(1);
    } finally {
        rl.close();
    }
}

// Run setup if called directly
if (require.main === module) {
    main();
}

module.exports = { main };
