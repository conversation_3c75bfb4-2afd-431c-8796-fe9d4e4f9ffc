const express = require('express');
const { getDatabase } = require('../database/init');
const { requireAuth } = require('./auth');
const router = express.Router();

// Apply authentication middleware
router.use(requireAuth);

// Start a new scan
router.post('/start', async (req, res) => {
    try {
        const { target_url, scan_types, options } = req.body;
        const userId = req.session.userId;

        if (!target_url || !scan_types || scan_types.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Target URL and scan types are required'
            });
        }

        // Validate URL format
        try {
            new URL(target_url);
        } catch (error) {
            return res.status(400).json({
                success: false,
                message: 'Invalid URL format'
            });
        }

        const db = getDatabase();
        
        // Create scan session
        db.run(`INSERT INTO scan_sessions (user_id, target_url, scan_type, status, created_at) 
                VALUES (?, ?, ?, 'running', CURRENT_TIMESTAMP)`, 
            [userId, target_url, JSON.stringify(scan_types)], 
            function(err) {
                if (err) {
                    db.close();
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                const sessionId = this.lastID;
                db.close();

                // Start the actual scanning process
                startScanProcess(sessionId, target_url, scan_types, options);

                res.json({
                    success: true,
                    message: 'Scan started successfully',
                    sessionId
                });
            }
        );

    } catch (error) {
        console.error('Start scan error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to start scan'
        });
    }
});

// Get scan status
router.get('/status/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;
        const userId = req.session.userId;
        
        const db = getDatabase();
        
        db.get('SELECT * FROM scan_sessions WHERE id = ? AND user_id = ?', 
            [sessionId, userId], 
            (err, session) => {
                if (err) {
                    db.close();
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                if (!session) {
                    db.close();
                    return res.status(404).json({
                        success: false,
                        message: 'Scan session not found'
                    });
                }

                // Get vulnerabilities for this session
                db.all('SELECT * FROM vulnerabilities WHERE session_id = ?', 
                    [sessionId], 
                    (err, vulnerabilities) => {
                        db.close();
                        
                        if (err) {
                            return res.status(500).json({
                                success: false,
                                message: 'Database error'
                            });
                        }

                        res.json({
                            success: true,
                            session: {
                                ...session,
                                vulnerabilities
                            }
                        });
                    }
                );
            }
        );

    } catch (error) {
        console.error('Get scan status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get scan status'
        });
    }
});

// Get user's scan history
router.get('/history', async (req, res) => {
    try {
        const userId = req.session.userId;
        const { page = 1, limit = 10 } = req.query;
        const offset = (page - 1) * limit;
        
        const db = getDatabase();
        
        db.all(`SELECT s.*, COUNT(v.id) as vulnerability_count 
                FROM scan_sessions s 
                LEFT JOIN vulnerabilities v ON s.id = v.session_id 
                WHERE s.user_id = ? 
                GROUP BY s.id 
                ORDER BY s.created_at DESC 
                LIMIT ? OFFSET ?`, 
            [userId, limit, offset], 
            (err, sessions) => {
                if (err) {
                    db.close();
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                // Get total count
                db.get('SELECT COUNT(*) as total FROM scan_sessions WHERE user_id = ?', 
                    [userId], 
                    (err, countResult) => {
                        db.close();
                        
                        if (err) {
                            return res.status(500).json({
                                success: false,
                                message: 'Database error'
                            });
                        }

                        res.json({
                            success: true,
                            sessions,
                            pagination: {
                                page: parseInt(page),
                                limit: parseInt(limit),
                                total: countResult.total,
                                pages: Math.ceil(countResult.total / limit)
                            }
                        });
                    }
                );
            }
        );

    } catch (error) {
        console.error('Get scan history error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get scan history'
        });
    }
});

// Stop a running scan
router.post('/stop/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;
        const userId = req.session.userId;
        
        const db = getDatabase();
        
        db.run('UPDATE scan_sessions SET status = "stopped", completed_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?', 
            [sessionId, userId], 
            function(err) {
                db.close();
                
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                if (this.changes === 0) {
                    return res.status(404).json({
                        success: false,
                        message: 'Scan session not found'
                    });
                }

                res.json({
                    success: true,
                    message: 'Scan stopped successfully'
                });
            }
        );

    } catch (error) {
        console.error('Stop scan error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to stop scan'
        });
    }
});

// Delete scan session
router.delete('/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;
        const userId = req.session.userId;
        
        const db = getDatabase();
        
        // Delete vulnerabilities first (foreign key constraint)
        db.run('DELETE FROM vulnerabilities WHERE session_id = ?', [sessionId], (err) => {
            if (err) {
                db.close();
                return res.status(500).json({
                    success: false,
                    message: 'Database error'
                });
            }

            // Delete scan session
            db.run('DELETE FROM scan_sessions WHERE id = ? AND user_id = ?', 
                [sessionId, userId], 
                function(err) {
                    db.close();
                    
                    if (err) {
                        return res.status(500).json({
                            success: false,
                            message: 'Database error'
                        });
                    }

                    if (this.changes === 0) {
                        return res.status(404).json({
                            success: false,
                            message: 'Scan session not found'
                        });
                    }

                    res.json({
                        success: true,
                        message: 'Scan session deleted successfully'
                    });
                }
            );
        });

    } catch (error) {
        console.error('Delete scan error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete scan session'
        });
    }
});

// Actual scanning process using real tools
async function startScanProcess(sessionId, targetUrl, scanTypes, options = {}) {
    const db = getDatabase();

    try {
        console.log(`🚀 Starting real scan for ${targetUrl} with types: ${scanTypes.join(', ')}`);

        // Update status to running
        db.run('UPDATE scan_sessions SET status = "running" WHERE id = ?', [sessionId]);

        const vulnerabilities = [];

        // Import scanning tools
        const VulnerabilityScanner = require('../tools/vulnerability-scanner');
        const SSLScanner = require('../tools/ssl-scanner');
        const BruteForceScanner = require('../tools/brute-force');

        // Initialize scanners
        const vulnScanner = new VulnerabilityScanner(options);
        const sslScanner = new SSLScanner();
        const bruteScanner = new BruteForceScanner(options);

        // Run scans based on selected types
        for (const scanType of scanTypes) {
            try {
                console.log(`🔍 Running ${scanType} scan...`);

                switch (scanType) {
                    case 'sql_injection':
                        await vulnScanner.scanSQLInjection(targetUrl);
                        vulnerabilities.push(...vulnScanner.getVulnerabilities().filter(v => v.type.includes('SQL')));
                        break;

                    case 'xss':
                        await vulnScanner.scanXSS(targetUrl);
                        vulnerabilities.push(...vulnScanner.getVulnerabilities().filter(v => v.type.includes('XSS')));
                        break;

                    case 'csrf':
                        await vulnScanner.scanCSRF(targetUrl);
                        vulnerabilities.push(...vulnScanner.getVulnerabilities().filter(v => v.type.includes('CSRF')));
                        break;

                    case 'directory_traversal':
                        await vulnScanner.scanDirectoryTraversal(targetUrl);
                        vulnerabilities.push(...vulnScanner.getVulnerabilities().filter(v => v.type.includes('Directory')));
                        break;

                    case 'ssl_check':
                        const sslVulns = await sslScanner.scanSSL(targetUrl);
                        vulnerabilities.push(...sslVulns);
                        break;

                    case 'brute_force':
                        const bruteVulns = await bruteScanner.scanBruteForce(targetUrl, ['login', 'directories', 'files']);
                        vulnerabilities.push(...bruteVulns);
                        break;

                    default:
                        console.log(`⚠️  Unknown scan type: ${scanType}`);
                }

            } catch (scanError) {
                console.error(`Error in ${scanType} scan:`, scanError.message);

                // Add error as a vulnerability
                vulnerabilities.push({
                    type: 'Scan Error',
                    severity: 'Low',
                    description: `Error occurred during ${scanType} scan: ${scanError.message}`,
                    url: targetUrl,
                    payload: 'N/A',
                    evidence: scanError.stack || scanError.message,
                    recommendation: 'Check target accessibility and scan configuration'
                });
            }
        }

        // Remove duplicates
        const uniqueVulns = vulnerabilities.filter((vuln, index, self) =>
            index === self.findIndex(v => v.type === vuln.type && v.url === vuln.url && v.payload === vuln.payload)
        );

        // Save vulnerabilities to database
        for (const vuln of uniqueVulns) {
            db.run(`INSERT INTO vulnerabilities
                    (session_id, vulnerability_type, severity, description, url, payload, evidence, recommendation)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [sessionId, vuln.type, vuln.severity, vuln.description, vuln.url,
                 vuln.payload || 'N/A', vuln.evidence || '', vuln.recommendation || '']
            );
        }

        // Update session as completed
        db.run(`UPDATE scan_sessions
                SET status = "completed", completed_at = CURRENT_TIMESTAMP, results = ?
                WHERE id = ?`,
            [JSON.stringify({
                vulnerabilities_found: uniqueVulns.length,
                scan_types: scanTypes,
                target_url: targetUrl
            }), sessionId]
        );

        console.log(`✅ Real scan completed for session ${sessionId}. Found ${uniqueVulns.length} vulnerabilities.`);

    } catch (error) {
        console.error('Scan process error:', error);

        // Update session as failed
        db.run('UPDATE scan_sessions SET status = "failed", completed_at = CURRENT_TIMESTAMP WHERE id = ?',
            [sessionId]
        );
    } finally {
        db.close();
    }
}



module.exports = router;
