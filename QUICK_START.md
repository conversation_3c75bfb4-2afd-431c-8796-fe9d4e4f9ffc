# 🚀 دليل التشغيل السريع - CyberWeb Hunter

## ⚡ التشغيل السريع (أسهل طريقة)

### 🖥️ Windows:
1. **انقر مرتين** على ملف `start.bat`
2. **انتظر** حتى يتم تثبيت التبعيات وتشغيل الخادم
3. **المتصفح سيفتح تلقائياً** بعد ثانيتين

### 🐧 Linux / 🍎 macOS:
```bash
# اجعل الملف قابل للتنفيذ
chmod +x start.sh

# شغل الملف
./start.sh
```

## 🌐 الوصول للتطبيق

بعد التشغيل، ستحصل على الروابط التالية:

### 📱 الروابط الرئيسية:
- **الصفحة الرئيسية**: http://localhost:3000
- **لوحة التحكم**: http://localhost:3000/dashboard
- **لوحة المسؤول**: http://localhost:3000/admin

### 🔐 بيانات تسجيل الدخول:
```
👤 اسم المستخدم: admin
🔑 كلمة المرور: JaMaL@123
```

## 🐳 التشغيل باستخدام Docker

إذا كان لديك Docker مثبت:

```bash
# بناء وتشغيل الحاويات
docker-compose up -d

# عرض السجلات
docker-compose logs -f

# إيقاف الحاويات
docker-compose down
```

## 📋 خطوات التشغيل اليدوي

إذا كنت تفضل التحكم الكامل:

### 1. تثبيت Node.js
- حمّل من: https://nodejs.org
- تأكد من الإصدار: `node --version` (يجب أن يكون 16+)

### 2. تثبيت التبعيات
```bash
npm install
```

### 3. تشغيل الخادم
```bash
npm start
```

### 4. فتح المتصفح
انتقل إلى: http://localhost:3000

## 🔧 استكشاف الأخطاء

### ❌ خطأ: "npm is not recognized"
**الحل**: تأكد من تثبيت Node.js وإعادة تشغيل Terminal

### ❌ خطأ: "Port 3000 is already in use"
**الحل**: 
```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID_NUMBER> /F

# Linux/Mac
lsof -ti:3000 | xargs kill -9
```

### ❌ خطأ: "Cannot find module"
**الحل**:
```bash
rm -rf node_modules package-lock.json
npm install
```

## 🎯 أول خطوات بعد التشغيل

### 1. تسجيل الدخول
- استخدم: `admin` / `JaMaL@123`
- **مهم**: غيّر كلمة المرور فوراً!

### 2. استكشاف الواجهة
- **لوحة التحكم**: للمستخدمين العاديين
- **لوحة المسؤول**: لإدارة النظام

### 3. تجربة فحص سريع
- أدخل رابط موقعك الشخصي
- اختر أنواع الفحص
- ابدأ الفحص واعرض النتائج

## ⚠️ تذكير مهم

### 🚨 الاستخدام القانوني فقط:
- ✅ اختبار مواقعك الشخصية
- ✅ الحصول على إذن كتابي
- ✅ الاستخدام التعليمي
- ❌ اختبار مواقع بدون إذن

### 🔐 الأمان:
- غيّر كلمة مرور المسؤول
- استخدم HTTPS في الإنتاج
- احم قاعدة البيانات
- راقب السجلات

## 📞 المساعدة

إذا واجهت مشاكل:
1. راجع ملف `FAQ.md`
2. تحقق من ملف `INSTALL.md`
3. اقرأ `SECURITY.md` للإرشادات القانونية

---

<div align="center">

**🛡️ مرحباً بك في عالم الأمان السيبراني الأخلاقي!**

*استخدم بمسؤولية - الأمان أولاً*

</div>
