const express = require('express');
const { getDatabase } = require('../database/init');
const { requireAuth, requireAdmin } = require('./auth');
const router = express.Router();

// Apply authentication middleware to all admin routes
router.use(requireAuth);
router.use(requireAdmin);

// Get dashboard statistics
router.get('/stats', async (req, res) => {
    try {
        const db = getDatabase();
        
        // Get various statistics
        const stats = await Promise.all([
            new Promise((resolve, reject) => {
                db.get('SELECT COUNT(*) as count FROM users', (err, result) => {
                    if (err) reject(err);
                    else resolve({ totalUsers: result.count });
                });
            }),
            new Promise((resolve, reject) => {
                db.get('SELECT COUNT(*) as count FROM scan_sessions', (err, result) => {
                    if (err) reject(err);
                    else resolve({ totalScans: result.count });
                });
            }),
            new Promise((resolve, reject) => {
                db.get('SELECT COUNT(*) as count FROM vulnerabilities', (err, result) => {
                    if (err) reject(err);
                    else resolve({ totalVulnerabilities: result.count });
                });
            }),
            new Promise((resolve, reject) => {
                db.get('SELECT COUNT(*) as count FROM scan_sessions WHERE status = "completed"', (err, result) => {
                    if (err) reject(err);
                    else resolve({ completedScans: result.count });
                });
            })
        ]);

        const combinedStats = Object.assign({}, ...stats);
        
        db.close();
        
        res.json({
            success: true,
            stats: combinedStats
        });

    } catch (error) {
        console.error('Stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch statistics'
        });
    }
});

// Get all users
router.get('/users', async (req, res) => {
    try {
        const db = getDatabase();
        
        db.all('SELECT id, username, email, role, created_at, last_login, is_active FROM users ORDER BY created_at DESC', 
            (err, users) => {
                db.close();
                
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                res.json({
                    success: true,
                    users
                });
            }
        );

    } catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch users'
        });
    }
});

// Get all scan sessions
router.get('/scans', async (req, res) => {
    try {
        const db = getDatabase();
        
        db.all(`SELECT s.*, u.username 
                FROM scan_sessions s 
                LEFT JOIN users u ON s.user_id = u.id 
                ORDER BY s.created_at DESC 
                LIMIT 100`, 
            (err, scans) => {
                db.close();
                
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                res.json({
                    success: true,
                    scans
                });
            }
        );

    } catch (error) {
        console.error('Get scans error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch scans'
        });
    }
});

// Get vulnerabilities
router.get('/vulnerabilities', async (req, res) => {
    try {
        const db = getDatabase();
        
        db.all(`SELECT v.*, s.target_url, u.username 
                FROM vulnerabilities v 
                LEFT JOIN scan_sessions s ON v.session_id = s.id 
                LEFT JOIN users u ON s.user_id = u.id 
                ORDER BY v.created_at DESC 
                LIMIT 100`, 
            (err, vulnerabilities) => {
                db.close();
                
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                res.json({
                    success: true,
                    vulnerabilities
                });
            }
        );

    } catch (error) {
        console.error('Get vulnerabilities error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch vulnerabilities'
        });
    }
});

// Update user status
router.put('/users/:id/status', async (req, res) => {
    try {
        const { id } = req.params;
        const { is_active } = req.body;
        
        const db = getDatabase();
        
        db.run('UPDATE users SET is_active = ? WHERE id = ?', [is_active, id], function(err) {
            db.close();
            
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Database error'
                });
            }

            res.json({
                success: true,
                message: 'User status updated successfully'
            });
        });

    } catch (error) {
        console.error('Update user status error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update user status'
        });
    }
});

// Delete user
router.delete('/users/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        // Prevent deleting admin user
        if (id == 1) {
            return res.status(400).json({
                success: false,
                message: 'Cannot delete admin user'
            });
        }
        
        const db = getDatabase();
        
        db.run('DELETE FROM users WHERE id = ?', [id], function(err) {
            db.close();
            
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Database error'
                });
            }

            res.json({
                success: true,
                message: 'User deleted successfully'
            });
        });

    } catch (error) {
        console.error('Delete user error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to delete user'
        });
    }
});

// Get system settings
router.get('/settings', async (req, res) => {
    try {
        const db = getDatabase();
        
        db.all('SELECT * FROM settings ORDER BY key', (err, settings) => {
            db.close();
            
            if (err) {
                return res.status(500).json({
                    success: false,
                    message: 'Database error'
                });
            }

            res.json({
                success: true,
                settings
            });
        });

    } catch (error) {
        console.error('Get settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch settings'
        });
    }
});

// Update system settings
router.put('/settings', async (req, res) => {
    try {
        const { settings } = req.body;
        const db = getDatabase();
        
        // Update each setting
        const promises = Object.entries(settings).map(([key, value]) => {
            return new Promise((resolve, reject) => {
                db.run('INSERT OR REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, CURRENT_TIMESTAMP)', 
                    [key, value], (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });
        });

        await Promise.all(promises);
        db.close();

        res.json({
            success: true,
            message: 'Settings updated successfully'
        });

    } catch (error) {
        console.error('Update settings error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to update settings'
        });
    }
});

// Get recent activity
router.get('/activity', async (req, res) => {
    try {
        const db = getDatabase();
        
        db.all(`SELECT 
                    'scan' as type,
                    s.id,
                    s.target_url as description,
                    s.created_at,
                    u.username
                FROM scan_sessions s
                LEFT JOIN users u ON s.user_id = u.id
                ORDER BY s.created_at DESC
                LIMIT 20`, 
            (err, activities) => {
                db.close();
                
                if (err) {
                    return res.status(500).json({
                        success: false,
                        message: 'Database error'
                    });
                }

                res.json({
                    success: true,
                    activities
                });
            }
        );

    } catch (error) {
        console.error('Get activity error:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch activity'
        });
    }
});

module.exports = router;
