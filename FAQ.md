# ❓ الأسئلة الشائعة - CyberWeb Hunter

## 🚀 البدء والتثبيت

### س: ما هي متطلبات النظام لتشغيل CyberWeb Hunter؟
**ج:** 
- **Node.js** الإصدار 16 أو أحدث
- **RAM**: 2 جيجابايت على الأقل
- **مساحة القرص**: 1 جيجابايت متاحة
- **نظام التشغيل**: Windows, macOS, Linux

### س: كيف أقوم بتثبيت Node.js؟
**ج:** 
1. انتقل إلى [nodejs.org](https://nodejs.org)
2. حمّل الإصدار LTS
3. اتبع تعليمات التثبيت لنظام التشغيل الخاص بك
4. تحقق من التثبيت: `node --version`

### س: أحصل على خطأ "npm is not recognized"، ما الحل؟
**ج:** 
- تأكد من تثبيت Node.js بشكل صحيح
- أعد تشغيل Terminal/Command Prompt
- أضف Node.js إلى متغير PATH في النظام
- أعد تشغيل الكمبيوتر إذا لزم الأمر

### س: كيف أقوم بتشغيل البرنامج؟
**ج:** 
```bash
# الطريقة الأولى
npm start

# الطريقة الثانية (Windows)
start.bat

# الطريقة الثالثة (Linux/Mac)
./start.sh
```

## 🔐 الأمان والقانونية

### س: هل استخدام CyberWeb Hunter قانوني؟
**ج:** 
نعم، ولكن بشروط:
- ✅ اختبار مواقعك الشخصية
- ✅ الحصول على إذن كتابي من مالك الموقع
- ✅ الاستخدام التعليمي في بيئات محاكاة
- ❌ اختبار مواقع بدون إذن

### س: كيف أحصل على إذن لاختبار موقع؟
**ج:** 
1. تواصل مع مالك الموقع أو مدير النظام
2. اطلب إذن كتابي يحدد:
   - نطاق الاختبار
   - التوقيت المسموح
   - أنواع الفحص المطلوبة
   - كيفية الإبلاغ عن النتائج

### س: ماذا أفعل إذا اكتشفت ثغرة أمنية؟
**ج:** 
1. **لا تستغل الثغرة** لأغراض ضارة
2. **أبلغ مالك الموقع** فوراً
3. **قدم تفاصيل واضحة** عن الثغرة
4. **اقترح حلول** للإصلاح
5. **امنح وقت كافي** للإصلاح قبل الكشف العام

## 🔧 الاستخدام والوظائف

### س: ما هي أنواع الفحص المتاحة؟
**ج:** 
- **SQL Injection**: فحص ثغرات حقن SQL
- **XSS**: اكتشاف ثغرات Cross-Site Scripting
- **CSRF**: فحص حماية Cross-Site Request Forgery
- **SSL/TLS**: تحليل شهادات الأمان
- **Directory Traversal**: فحص ثغرات تصفح المجلدات
- **Brute Force**: اختبار كلمات المرور الضعيفة

### س: كم يستغرق الفحص عادة؟
**ج:** 
يعتمد على:
- **حجم الموقع**: 1-30 دقيقة
- **أنواع الفحص المختارة**: 2-15 دقيقة لكل نوع
- **سرعة الاتصال**: يؤثر على الوقت
- **استجابة الخادم المستهدف**: قد يبطئ العملية

### س: كيف أقوم بإنشاء تقرير PDF؟
**ج:** 
1. انتظر انتهاء الفحص
2. انقر على "إنشاء تقرير PDF"
3. اختر التفاصيل المطلوبة
4. سيتم تحميل التقرير تلقائياً

### س: أين يتم حفظ التقارير؟
**ج:** 
- **في المتصفح**: مجلد التحميلات الافتراضي
- **على الخادم**: مجلد `reports/`
- **في لوحة التحكم**: قسم "التقارير"

## 🐛 استكشاف الأخطاء

### س: الخادم لا يبدأ، ما المشكلة؟
**ج:** 
تحقق من:
```bash
# تحقق من المنفذ
netstat -ano | findstr :3000

# تحقق من التبعيات
npm install

# تحقق من الأخطاء
npm start
```

### س: أحصل على خطأ "EADDRINUSE"؟
**ج:** 
المنفذ 3000 مستخدم:
```bash
# Windows
netstat -ano | findstr :3000
taskkill /PID <PID_NUMBER> /F

# Linux/Mac
lsof -ti:3000 | xargs kill -9

# أو استخدم منفذ آخر
PORT=8080 npm start
```

### س: الفحص يفشل دائماً، لماذا؟
**ج:** 
أسباب محتملة:
- **الموقع غير متاح**: تحقق من الرابط
- **جدار حماية**: قد يحجب الطلبات
- **مهلة انتهاء الوقت**: الموقع بطيء الاستجابة
- **حماية ضد البوتات**: الموقع يحجب الفحص التلقائي

### س: كيف أحل مشكلة "Cannot find module"؟
**ج:** 
```bash
# احذف node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install

# أو استخدم
npm run clean
```

## ⚙️ الإعدادات والتخصيص

### س: كيف أغير منفذ الخادم؟
**ج:** 
```bash
# مؤقتاً
PORT=8080 npm start

# دائماً - في ملف .env
PORT=8080
```

### س: كيف أضيف مستخدم جديد؟
**ج:** 
1. سجل دخول كمسؤول
2. انتقل إلى لوحة المسؤول
3. قسم "المستخدمون"
4. انقر "إضافة مستخدم جديد"

### س: كيف أغير كلمة مرور المسؤول؟
**ج:** 
1. سجل دخول كمسؤول
2. لوحة المسؤول → المستخدمون
3. اختر المستخدم admin
4. انقر "تعديل" وغيّر كلمة المرور

### س: كيف أفعل الوضع الخفي (Stealth Mode)؟
**ج:** 
في إعدادات الفحص المتقدم:
- ✅ فعّل "الوضع الخفي"
- ✅ استخدم البروكسي
- ✅ قلل سرعة الفحص

## 📊 البيانات والنسخ الاحتياطي

### س: أين يتم حفظ البيانات؟
**ج:** 
- **قاعدة البيانات**: `database/cyberweb.db`
- **التقارير**: `reports/`
- **الملفات المرفوعة**: `uploads/`
- **السجلات**: `logs/`

### س: كيف أقوم بنسخ احتياطي؟
**ج:** 
```bash
# تلقائياً
npm run backup

# يدوياً
cp -r database/ backup-$(date +%Y%m%d)/
```

### س: كيف أستعيد نسخة احتياطية؟
**ج:** 
```bash
npm run restore
# اتبع التعليمات التفاعلية
```

## 🔄 التحديث والصيانة

### س: كيف أحدث CyberWeb Hunter؟
**ج:** 
```bash
# تحديث التبعيات
npm update

# تحديث الكود (إذا كان من Git)
git pull origin main
npm install
```

### س: كيف أنظف الملفات القديمة؟
**ج:** 
```bash
# تنظيف التقارير القديمة (أكثر من 30 يوم)
find reports/ -name "*.pdf" -mtime +30 -delete

# تنظيف السجلات القديمة
find logs/ -name "*.log" -mtime +7 -delete
```

## 🐳 Docker والحاويات

### س: كيف أشغل CyberWeb Hunter في Docker؟
**ج:** 
```bash
# بناء الصورة
docker build -t cyberweb-hunter .

# تشغيل الحاوية
docker run -p 3000:3000 cyberweb-hunter

# أو استخدم docker-compose
docker-compose up -d
```

### س: كيف أصل للبيانات في Docker؟
**ج:** 
```bash
# دخول الحاوية
docker exec -it cyberweb-hunter /bin/sh

# نسخ الملفات
docker cp cyberweb-hunter:/app/database ./backup/
```

## 📞 الدعم والمساعدة

### س: أين أجد المزيد من المساعدة؟
**ج:** 
- 📖 **الوثائق**: اقرأ ملف README.md
- 🐛 **الأخطاء**: [GitHub Issues](https://github.com/cyberweb-hunter/issues)
- 💬 **المجتمع**: [Discord Server](https://discord.gg/cyberweb-hunter)
- 📧 **الدعم**: <EMAIL>

### س: كيف أساهم في تطوير المشروع؟
**ج:** 
1. Fork المستودع على GitHub
2. أنشئ branch جديد للميزة
3. اكتب الكود واختبره
4. أرسل Pull Request
5. انتظر المراجعة والموافقة

### س: هل يمكنني استخدام CyberWeb Hunter تجارياً؟
**ج:** 
نعم، المشروع مرخص تحت MIT License، لكن:
- اقرأ شروط الترخيص بعناية
- تأكد من الامتثال للقوانين المحلية
- استخدمه بمسؤولية أخلاقية

---

<div align="center">

**لم تجد إجابة لسؤالك؟**

[📧 تواصل معنا](mailto:<EMAIL>) | [💬 انضم للمجتمع](https://discord.gg/cyberweb-hunter)

</div>
