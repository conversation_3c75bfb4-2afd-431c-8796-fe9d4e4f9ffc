# CyberWeb Hunter Docker Configuration

# Use official Node.js runtime as base image
FROM node:18-alpine

# Set working directory in container
WORKDIR /app

# Create app user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S cyberweb -u 1001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy application code
COPY . .

# Create required directories
RUN mkdir -p database reports uploads logs

# Set proper permissions
RUN chown -R cyberweb:nodejs /app
RUN chmod -R 755 /app

# Switch to non-root user
USER cyberweb

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Start the application
CMD ["npm", "start"]

# Labels for metadata
LABEL maintainer="CyberWeb Hunter Team"
LABEL version="1.0.0"
LABEL description="Ethical Penetration Testing Tool"
LABEL org.opencontainers.image.source="https://github.com/cyberweb-hunter/cyberweb-hunter"
