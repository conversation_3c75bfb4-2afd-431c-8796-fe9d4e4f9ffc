@echo off
title CyberWeb Hunter - Ethical Penetration Testing Tool
color 0A

echo.
echo  ========================================
echo  🛡️  CyberWeb Hunter v1.0
echo  ========================================
echo  أداة اختبار الاختراق الأخلاقي
echo  Ethical Penetration Testing Tool
echo  ========================================
echo.

echo [INFO] Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Node.js is not installed!
    echo [INFO] Please download and install Node.js from: https://nodejs.org
    echo [INFO] Then restart this script.
    pause
    exit /b 1
)

echo [SUCCESS] Node.js is installed
node --version

echo.
echo [INFO] Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm is not available!
    echo [INFO] Please reinstall Node.js with npm included.
    pause
    exit /b 1
)

echo [SUCCESS] npm is available
npm --version

echo.
echo [INFO] Checking if dependencies are installed...
if not exist "node_modules" (
    echo [INFO] Installing dependencies... This may take a few minutes.
    echo [INFO] Please wait...
    npm install
    if %errorlevel% neq 0 (
        echo [ERROR] Failed to install dependencies!
        echo [INFO] Please check your internet connection and try again.
        pause
        exit /b 1
    )
    echo [SUCCESS] Dependencies installed successfully!
) else (
    echo [SUCCESS] Dependencies already installed
)

echo.
echo [INFO] Creating required directories...
if not exist "database" mkdir database
if not exist "reports" mkdir reports
if not exist "uploads" mkdir uploads
if not exist "logs" mkdir logs

echo.
echo [INFO] Starting CyberWeb Hunter server...
echo [INFO] Please wait while the server initializes...
echo.
echo ========================================
echo  🚀 Server Starting...
echo ========================================
echo.
echo [INFO] Access URLs:
echo  📱 Main Page: http://localhost:3000
echo  📊 Dashboard: http://localhost:3000/dashboard  
echo  ⚙️  Admin Panel: http://localhost:3000/admin
echo.
echo [INFO] Default Admin Credentials:
echo  👤 Username: admin
echo  🔑 Password: JaMaL@123
echo.
echo ========================================
echo  ⚠️  LEGAL WARNING
echo ========================================
echo  This tool is for EDUCATIONAL and ETHICAL
echo  purposes ONLY. Always get permission
echo  before testing any website.
echo ========================================
echo.
echo [INFO] Press Ctrl+C to stop the server
echo [INFO] Browser will open automatically in 2 seconds...
echo.

npm start

echo.
echo [INFO] Server stopped.
pause
