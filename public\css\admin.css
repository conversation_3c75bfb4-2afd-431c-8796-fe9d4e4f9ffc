/* Admin Panel Styles for CyberWeb Hunter */

/* Admin Badge */
.admin-badge {
    background: linear-gradient(45deg, var(--danger-color), #dc3545);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

/* Admin Stats Grid */
.admin-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.admin-stat-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.admin-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 255, 65, 0.2);
    border-color: var(--primary-color);
}

.admin-stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.admin-stat-card .stat-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--dark-bg);
}

.admin-stat-card .stat-content h3 {
    font-size: 2.5rem;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.admin-stat-card .stat-content p {
    color: var(--text-secondary);
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.stat-change {
    color: var(--success-color);
    font-size: 0.8rem;
    font-weight: 600;
}

/* Admin Dashboard Grid */
.admin-dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.admin-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
}

.admin-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.admin-card h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.3rem;
}

/* Chart Container */
.chart-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    color: var(--text-secondary);
}

/* Vulnerability Types */
.vulnerability-types {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.vuln-type-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.vuln-type-name {
    flex: 1;
    color: var(--text-primary);
    font-weight: 600;
}

.vuln-type-count {
    color: var(--text-secondary);
    font-size: 0.9rem;
    min-width: 40px;
}

.vuln-type-bar {
    flex: 2;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.vuln-type-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Activity List */
.activity-list {
    max-height: 250px;
    overflow-y: auto;
}

.activity-item {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--dark-bg);
    font-size: 0.9rem;
}

.activity-content {
    flex: 1;
}

.activity-content h5 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.activity-content p {
    color: var(--text-secondary);
    font-size: 0.8rem;
}

/* Admin Controls */
.admin-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.filter-controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-controls select,
.filter-controls input {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
}

.search-box input {
    padding: 0.5rem 2.5rem 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    color: var(--text-secondary);
}

/* Admin Table */
.admin-table-container {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    overflow: hidden;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
}

.admin-table th {
    background: rgba(0, 255, 65, 0.1);
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.9rem;
}

.admin-table td {
    color: var(--text-primary);
    font-size: 0.9rem;
}

.admin-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
}

.admin-table .loading {
    text-align: center;
    color: var(--text-secondary);
    padding: 2rem;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-active { background: var(--success-color); color: var(--dark-bg); }
.status-inactive { background: var(--text-secondary); color: white; }
.status-completed { background: var(--success-color); color: var(--dark-bg); }
.status-running { background: var(--warning-color); color: var(--dark-bg); }
.status-failed { background: var(--danger-color); color: white; }
.status-stopped { background: var(--text-secondary); color: white; }

/* Severity Badges */
.severity-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.severity-high { background: var(--danger-color); color: white; }
.severity-medium { background: var(--warning-color); color: var(--dark-bg); }
.severity-low { background: var(--success-color); color: var(--dark-bg); }

/* Action Buttons */
.action-btn {
    padding: 0.25rem 0.5rem;
    border: none;
    border-radius: 5px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.action-btn-view {
    background: var(--accent-color);
    color: white;
}

.action-btn-edit {
    background: var(--warning-color);
    color: var(--dark-bg);
}

.action-btn-delete {
    background: var(--danger-color);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Reports Grid */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
}

.report-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 255, 65, 0.2);
    border-color: var(--primary-color);
}

.report-card h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.report-card p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.report-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

/* Settings Grid */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.settings-card {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 2rem;
}

.settings-card h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.setting-item {
    margin-bottom: 1.5rem;
}

.setting-item label {
    display: block;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
}

.setting-item input[type="number"],
.setting-item input[type="text"] {
    width: 100%;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.setting-item input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
}

.settings-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    margin: 5% auto;
    padding: 0;
    width: 90%;
    max-width: 500px;
    animation: modalSlideIn 0.3s ease;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    color: var(--primary-color);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-controls {
        justify-content: center;
    }
    
    .search-box input {
        min-width: 100%;
    }
    
    .admin-table-container {
        overflow-x: auto;
    }
    
    .admin-table {
        min-width: 800px;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
}
