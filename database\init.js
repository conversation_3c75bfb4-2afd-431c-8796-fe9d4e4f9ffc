const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const path = require('path');
const fs = require('fs-extra');

const DB_PATH = path.join(__dirname, 'cyberweb.db');

// Initialize database
async function initDatabase() {
    try {
        // Ensure database directory exists
        await fs.ensureDir(path.dirname(DB_PATH));
        
        const db = new sqlite3.Database(DB_PATH);
        
        // Create tables
        await createTables(db);
        
        // Create default admin user
        await createDefaultAdmin(db);
        
        console.log('✅ Database initialized successfully');
        db.close();
        
    } catch (error) {
        console.error('❌ Database initialization failed:', error);
        throw error;
    }
}

// Create database tables
function createTables(db) {
    return new Promise((resolve, reject) => {
        db.serialize(() => {
            // Users table
            db.run(`CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                email TEXT,
                role TEXT DEFAULT 'user',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_login DATETIME,
                is_active BOOLEAN DEFAULT 1
            )`);

            // Scan sessions table
            db.run(`CREATE TABLE IF NOT EXISTS scan_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                target_url TEXT NOT NULL,
                scan_type TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                results TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                completed_at DATETIME,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`);

            // Vulnerabilities table
            db.run(`CREATE TABLE IF NOT EXISTS vulnerabilities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER,
                vulnerability_type TEXT NOT NULL,
                severity TEXT NOT NULL,
                description TEXT,
                url TEXT,
                payload TEXT,
                evidence TEXT,
                recommendation TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES scan_sessions (id)
            )`);

            // Reports table
            db.run(`CREATE TABLE IF NOT EXISTS reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_id INTEGER,
                report_type TEXT NOT NULL,
                file_path TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (session_id) REFERENCES scan_sessions (id)
            )`);

            // Settings table
            db.run(`CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`);

            // Proxy settings table
            db.run(`CREATE TABLE IF NOT EXISTS proxy_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                proxy_type TEXT NOT NULL,
                host TEXT NOT NULL,
                port INTEGER NOT NULL,
                username TEXT,
                password TEXT,
                is_active BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`);

            // Scan history table
            db.run(`CREATE TABLE IF NOT EXISTS scan_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                target_url TEXT NOT NULL,
                scan_types TEXT NOT NULL,
                vulnerabilities_found INTEGER DEFAULT 0,
                scan_duration INTEGER,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`, (err) => {
                if (err) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
    });
}

// Create default admin user
function createDefaultAdmin(db) {
    return new Promise(async (resolve, reject) => {
        try {
            const hashedPassword = await bcrypt.hash('JaMaL@123', 12);
            
            db.run(`INSERT OR IGNORE INTO users (username, password, email, role) 
                    VALUES (?, ?, ?, ?)`, 
                ['admin', hashedPassword, '<EMAIL>', 'admin'], 
                function(err) {
                    if (err) {
                        reject(err);
                    } else {
                        console.log('✅ Default admin user created: admin / JaMaL@123');
                        resolve();
                    }
                }
            );
        } catch (error) {
            reject(error);
        }
    });
}

// Get database connection
function getDatabase() {
    return new sqlite3.Database(DB_PATH);
}

module.exports = {
    initDatabase,
    getDatabase,
    DB_PATH
};
