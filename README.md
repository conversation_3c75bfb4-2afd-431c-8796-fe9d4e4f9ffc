# 🛡️ CyberWeb Hunter - أداة اختبار الاختراق الأخلاقي

<div align="center">

![CyberWeb Hunter Logo](https://img.shields.io/badge/CyberWeb-Hunter-00ff41?style=for-the-badge&logo=shield&logoColor=white)

[![Version](https://img.shields.io/badge/version-1.0.0-blue.svg)](https://github.com/cyberweb-hunter/cyberweb-hunter)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Node.js](https://img.shields.io/badge/node.js-16+-brightgreen.svg)](https://nodejs.org/)
[![Security](https://img.shields.io/badge/security-ethical%20hacking-red.svg)](https://github.com/cyberweb-hunter/cyberweb-hunter)

**أداة اختبار الاختراق الأخلاقي الأكثر تطوراً للمواقع الإلكترونية**

[🚀 البدء السريع](#-البدء-السريع) • [📖 الوثائق](#-الوثائق) • [🔧 التثبيت](#-التثبيت) • [⚠️ تحذير قانوني](#️-تحذير-قانوني)

</div>

---

## 🎯 نظرة عامة

**CyberWeb Hunter** هي أداة شاملة لاختبار الاختراق الأخلاقي مصممة لمساعدة خبراء الأمن السيبراني والباحثين في اكتشاف الثغرات الأمنية في المواقع الإلكترونية. تتميز الأداة بواجهة مستخدم حديثة وأدوات فحص متقدمة.

### ✨ المميزات الرئيسية

- 🔍 **فحص شامل للثغرات**: SQL Injection, XSS, CSRF, Directory Traversal
- 🔒 **تحليل SSL/TLS**: فحص شهادات الأمان وإعدادات التشفير
- 🔨 **أدوات Brute Force**: اختبار كلمات المرور الضعيفة
- 🕵️ **إخفاء الهوية**: دعم البروكسي والشبكات المجهولة
- 📊 **تقارير PDF**: تقارير مفصلة واحترافية
- 👥 **إدارة المستخدمين**: نظام مصادقة متقدم
- 🎨 **واجهة أسطورية**: تصميم Matrix مع تأثيرات بصرية

## 🔧 التثبيت

### المتطلبات الأساسية

- **Node.js** (الإصدار 16 أو أحدث)
- **npm** أو **yarn**
- **Git**

### خطوات التثبيت

1. **استنساخ المستودع**
```bash
git clone https://github.com/cyberweb-hunter/cyberweb-hunter.git
cd cyberweb-hunter
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل الخادم**
```bash
npm start
```

4. **الوصول للتطبيق**
```
http://localhost:3000
```

### بيانات تسجيل الدخول الافتراضية

- **المسؤول**: `admin` / `JaMaL@123`

## 🚀 البدء السريع

### 1. تسجيل الدخول
- افتح المتصفح وانتقل إلى `http://localhost:3000`
- استخدم بيانات المسؤول للدخول

### 2. بدء فحص سريع
- انتقل إلى لوحة التحكم
- أدخل رابط الموقع المراد فحصه
- اختر أنواع الفحص المطلوبة
- انقر على "بدء الفحص"

### 3. عرض النتائج
- راقب تقدم الفحص في الوقت الفعلي
- اعرض الثغرات المكتشفة
- أنشئ تقرير PDF للنتائج

## 📖 الوثائق

### أنواع الفحص المدعومة

#### 🔍 فحص الثغرات الأساسية
- **SQL Injection**: اكتشاف ثغرات حقن SQL
- **Cross-Site Scripting (XSS)**: فحص ثغرات XSS المنعكسة والمخزنة
- **Cross-Site Request Forgery (CSRF)**: اكتشاف نقص حماية CSRF
- **Directory Traversal**: فحص ثغرات تصفح المجلدات

#### 🔒 فحص الأمان المتقدم
- **SSL/TLS Analysis**: تحليل شهادات الأمان
- **Security Headers**: فحص رؤوس الأمان المفقودة
- **Weak Ciphers**: اكتشاف خوارزميات التشفير الضعيفة

#### 🔨 أدوات Brute Force
- **Login Forms**: اختبار نماذج تسجيل الدخول
- **Directory Discovery**: اكتشاف المجلدات المخفية
- **File Discovery**: البحث عن الملفات الحساسة
- **Admin Panels**: اكتشاف لوحات الإدارة

### هيكل المشروع

```
cyberweb-hunter/
├── 📁 database/          # قاعدة البيانات والإعدادات
├── 📁 public/            # الملفات العامة والواجهة
│   ├── 📁 css/          # ملفات التنسيق
│   ├── 📁 js/           # ملفات JavaScript
│   └── 📁 images/       # الصور والأيقونات
├── 📁 routes/            # مسارات API
├── 📁 tools/             # أدوات الفحص
├── 📁 reports/           # التقارير المُنشأة
├── 📁 uploads/           # الملفات المرفوعة
└── 📁 logs/              # ملفات السجل
```

## 🔐 الأمان والخصوصية

### إعدادات الأمان
- تشفير كلمات المرور باستخدام bcrypt
- جلسات آمنة مع انتهاء صلاحية
- حماية من هجمات CSRF
- تحديد معدل الطلبات (Rate Limiting)

### إخفاء الهوية
- دعم البروكسي HTTP/HTTPS
- تكامل مع شبكة Tor
- تغيير User-Agent تلقائياً
- تأخير عشوائي بين الطلبات

## 📊 لوحة تحكم المسؤول

### المميزات الإدارية
- 📈 إحصائيات شاملة للنظام
- 👥 إدارة المستخدمين والأذونات
- 🔍 مراقبة عمليات الفحص
- 🐛 عرض الثغرات المكتشفة
- 📄 إدارة التقارير
- ⚙️ إعدادات النظام

## 🛠️ التطوير والمساهمة

### إعداد بيئة التطوير

```bash
# تثبيت التبعيات
npm install

# تشغيل في وضع التطوير
npm run dev

# تشغيل الاختبارات
npm test

# بناء للإنتاج
npm run build
```

### إضافة أدوات فحص جديدة

1. أنشئ ملف جديد في مجلد `tools/`
2. اتبع نمط الكلاسات الموجودة
3. أضف الأداة إلى `routes/scan.js`
4. اختبر الأداة الجديدة

## 📋 قائمة المهام

- [x] نظام المصادقة والتسجيل
- [x] فحص الثغرات الأساسية
- [x] تحليل SSL/TLS
- [x] أدوات Brute Force
- [x] نظام التقارير PDF
- [x] لوحة تحكم المسؤول
- [ ] دعم قواعد بيانات متعددة
- [ ] API للتكامل الخارجي
- [ ] تطبيق الهاتف المحمول
- [ ] نظام الإشعارات

## ⚠️ تحذير قانوني

**تحذير مهم**: هذه الأداة مخصصة للاستخدام التعليمي والأخلاقي فقط. يجب الحصول على إذن صريح من مالك الموقع قبل إجراء أي اختبارات أمنية.

### الاستخدام المسموح:
- ✅ اختبار مواقعك الشخصية
- ✅ البحث الأكاديمي والتعليمي
- ✅ اختبار الاختراق المصرح به
- ✅ تقييم الأمان للشركات

### الاستخدام المحظور:
- ❌ اختبار مواقع بدون إذن
- ❌ الأنشطة الإجرامية
- ❌ انتهاك الخصوصية
- ❌ إلحاق الضرر بالأنظمة

## 📞 الدعم والتواصل

- 📧 **البريد الإلكتروني**: <EMAIL>
- 💬 **Discord**: [انضم لخادمنا](https://discord.gg/cyberweb-hunter)
- 📱 **Telegram**: [@CyberWebHunter](https://t.me/CyberWebHunter)
- 🐛 **تقرير الأخطاء**: [GitHub Issues](https://github.com/cyberweb-hunter/cyberweb-hunter/issues)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للمزيد من التفاصيل.

---

<div align="center">

**صُنع بـ ❤️ من أجل مجتمع الأمن السيبراني**

[![GitHub Stars](https://img.shields.io/github/stars/cyberweb-hunter/cyberweb-hunter?style=social)](https://github.com/cyberweb-hunter/cyberweb-hunter)
[![GitHub Forks](https://img.shields.io/github/forks/cyberweb-hunter/cyberweb-hunter?style=social)](https://github.com/cyberweb-hunter/cyberweb-hunter)

</div>
