# CyberWeb Hunter Environment Configuration

# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_PATH=./database/cyberweb.db

# Session Configuration
SESSION_SECRET=cyberweb-hunter-secret-key-2024
SESSION_MAX_AGE=86400000

# Security Configuration
BCRYPT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_TIME=900000

# Scanning Configuration
MAX_CONCURRENT_SCANS=10
SCAN_TIMEOUT=300000
REQUEST_TIMEOUT=10000
MAX_REDIRECTS=5

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Proxy Configuration (Optional)
PROXY_HOST=
PROXY_PORT=
PROXY_USERNAME=
PROXY_PASSWORD=

# SSL Configuration (For Production)
SSL_KEY_PATH=
SSL_CERT_PATH=

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Email Configuration (Optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=

# Admin Configuration
DEFAULT_ADMIN_USERNAME=admin
DEFAULT_ADMIN_PASSWORD=JaMaL@123
DEFAULT_ADMIN_EMAIL=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads

# Report Configuration
REPORT_PATH=./reports
MAX_REPORTS_PER_USER=50

# Backup Configuration
BACKUP_PATH=./backups
AUTO_BACKUP_ENABLED=false
BACKUP_INTERVAL=86400000
