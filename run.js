#!/usr/bin/env node

/**
 * CyberWeb Hunter - Startup Script
 * This script handles the complete startup process
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader() {
    console.clear();
    log('╔══════════════════════════════════════════════════════════════╗', 'cyan');
    log('║                    🛡️  CyberWeb Hunter                      ║', 'cyan');
    log('║              Ethical Penetration Testing Tool               ║', 'cyan');
    log('║                        Version 1.0.0                        ║', 'cyan');
    log('╚══════════════════════════════════════════════════════════════╝', 'cyan');
    log('');
}

function checkNodeVersion() {
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    
    if (majorVersion < 16) {
        log('❌ Error: Node.js version 16 or higher is required', 'red');
        log(`   Current version: ${nodeVersion}`, 'yellow');
        log('   Please update Node.js from: https://nodejs.org', 'blue');
        process.exit(1);
    }
    
    log(`✅ Node.js version: ${nodeVersion}`, 'green');
}

function checkFiles() {
    const requiredFiles = [
        'server.js',
        'package.json',
        'database/init.js',
        'routes/auth.js',
        'routes/scan.js',
        'routes/admin.js',
        'routes/reports.js',
        'public/index.html',
        'public/dashboard.html',
        'public/admin.html'
    ];
    
    log('🔍 Checking required files...', 'blue');
    
    for (const file of requiredFiles) {
        if (!fs.existsSync(file)) {
            log(`❌ Missing file: ${file}`, 'red');
            process.exit(1);
        }
    }
    
    log('✅ All required files found', 'green');
}

function createDirectories() {
    const directories = [
        'database',
        'reports',
        'uploads',
        'logs',
        'backups'
    ];
    
    log('📁 Creating directories...', 'blue');
    
    directories.forEach(dir => {
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
            log(`   Created: ${dir}`, 'green');
        }
    });
    
    log('✅ Directories ready', 'green');
}

function installDependencies() {
    return new Promise((resolve, reject) => {
        log('📦 Installing dependencies...', 'blue');
        
        const npm = spawn('npm', ['install'], {
            stdio: 'pipe',
            shell: true
        });
        
        npm.stdout.on('data', (data) => {
            // Show progress dots
            process.stdout.write('.');
        });
        
        npm.stderr.on('data', (data) => {
            // Ignore warnings
        });
        
        npm.on('close', (code) => {
            console.log(''); // New line after dots
            if (code === 0) {
                log('✅ Dependencies installed successfully', 'green');
                resolve();
            } else {
                log('❌ Failed to install dependencies', 'red');
                reject(new Error('npm install failed'));
            }
        });
    });
}

function startServer() {
    return new Promise((resolve, reject) => {
        log('🚀 Starting CyberWeb Hunter server...', 'blue');
        log('');
        
        const server = spawn('node', ['server.js'], {
            stdio: 'inherit',
            shell: true
        });
        
        server.on('close', (code) => {
            if (code !== 0) {
                log(`❌ Server exited with code ${code}`, 'red');
                reject(new Error('Server failed to start'));
            }
        });
        
        // Don't resolve - let the server run
    });
}

async function main() {
    try {
        logHeader();
        
        log('🔧 Pre-flight checks...', 'magenta');
        checkNodeVersion();
        checkFiles();
        createDirectories();
        
        log('');
        log('📋 Setup process...', 'magenta');
        
        // Check if node_modules exists
        if (!fs.existsSync('node_modules')) {
            await installDependencies();
        } else {
            log('✅ Dependencies already installed', 'green');
        }
        
        log('');
        log('🎯 Starting application...', 'magenta');
        log('');
        log('📍 Access URLs:', 'bright');
        log('   🏠 Home Page:    http://localhost:3000', 'cyan');
        log('   📊 Dashboard:    http://localhost:3000/dashboard', 'cyan');
        log('   ⚙️  Admin Panel:  http://localhost:3000/admin', 'cyan');
        log('');
        log('🔐 Default Admin Credentials:', 'bright');
        log('   👤 Username: admin', 'yellow');
        log('   🔑 Password: JaMaL@123', 'yellow');
        log('');
        log('⚠️  Security Notice:', 'red');
        log('   • Change default password immediately', 'yellow');
        log('   • Use only for ethical testing with permission', 'yellow');
        log('   • Follow all applicable laws and regulations', 'yellow');
        log('');
        log('🔄 Server Status:', 'bright');
        
        await startServer();
        
    } catch (error) {
        log('');
        log('❌ Startup failed:', 'red');
        log(`   ${error.message}`, 'yellow');
        log('');
        log('💡 Troubleshooting:', 'blue');
        log('   1. Check Node.js version (16+ required)', 'cyan');
        log('   2. Run: npm install', 'cyan');
        log('   3. Check file permissions', 'cyan');
        log('   4. Ensure port 3000 is available', 'cyan');
        log('');
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    log('');
    log('🛑 Shutting down CyberWeb Hunter...', 'yellow');
    log('👋 Goodbye!', 'green');
    process.exit(0);
});

process.on('SIGTERM', () => {
    log('');
    log('🛑 Server terminated', 'yellow');
    process.exit(0);
});

// Start the application
main().catch(error => {
    log('');
    log('💥 Fatal error:', 'red');
    log(`   ${error.message}`, 'yellow');
    process.exit(1);
});
